<?php

namespace Pterodactyl\Http\Requests\Admin\Nest;

use Pterodactyl\Http\Requests\Admin\AdminFormRequest;

class StoreNestFormRequest extends AdminFormRequest
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|min:1|max:191|regex:/^[\w\- ]+$/',
            'description' => 'string|nullable',
        ];
    }
    public function passedValidation(): void // splitter
    { // splitter
        $this->merge([ // splitter
            'splitter_keep_nest' => $this->boolean('splitter_keep_nest') // splitter
        ]); // splitter
    } // splitter
}
