<?php

namespace Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Transformers;

use Pterodactyl\Transformers\Api\Client\BaseClientTransformer;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models\Template;

class TemplateTransformer extends BaseClientTransformer
{
    public function getResourceName(): string
    {
        return 'template';
    }

    public function transform(Template $template): array
    {
        return [
            'id' => $template->id,
            'name' => $template->name,
            'icon' => $template->icon,
            'category' => $template->category,
            'description' => $template->description,
            'full_description' => $template->full_description,
            'version' => $template->version,
            'author' => $template->author,
            'force_wipe' => $template->force_wipe,
            'force_reinstall' => $template->force_reinstall,
        ];
    }
}
