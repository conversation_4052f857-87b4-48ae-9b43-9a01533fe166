<?php

namespace Pterodactyl\Http\Controllers\Admin\MyPlugins;

use Prologue\Alerts\AlertsMessageBag;
use Pterodactyl\Http\Controllers\Controller;
use Pterodactyl\Models\discordAuthConfig;

class DiscordAuthController extends Controller
{

    /**
     * @var \Prologue\Alerts\AlertsMessageBag
     */
    protected $alert;

    /**
     * Constructor.
     */
    public function __construct(AlertsMessageBag $alert)
    {
        $this->alert = $alert;
    }

    /**
     * View index
     */
    public function index()
    {
        $config = discordAuthConfig::getAuthConfig();
        $redirect = route('auth.discord.callback');
        $login = route('auth.discord.login');

        return view('admin.myplugins.discord_auth.index', [
            'config' => $config,
            'redirect' => $redirect,
            'login' => $login,
        ]);
    }

    /**
     * Update config
     */
    public function update()
    {
        $enabled = request()->input('enabled');
        $client_id = request()->input('client_id');
        $client_secret = request()->input('client_secret');

        if ($enabled == 'true') {
            $enabled = 1;
        } else {
            $enabled = 0;
        }

        DiscordAuthConfig::where('key', 'enabled')->update(['value' => $enabled]);
        DiscordAuthConfig::where('key', 'client_id')->update(['value' => $client_id]);
        DiscordAuthConfig::where('key', 'client_secret')->update(['value' => $client_secret]);

        $this->alert->success('Updated Discord Auth Config.')->flash();

        return redirect()->route('admin.myplugins.discord_auth');
    }
}