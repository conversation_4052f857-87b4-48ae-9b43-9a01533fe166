<?php

namespace Pterodactyl\Tests\Unit\Services\Servers;

use Mockery as m;
use Tests\TestCase;
use Pterodactyl\Models\Server;
use Pterodactyl\Services\Servers\ServerPowerService;
use Pterodactyl\Repositories\Wings\DaemonPowerRepository;
use Pterodactyl\Repositories\Wings\DaemonCommandRepository;
use Pterodactyl\Exceptions\Http\Connection\DaemonConnectionException;

class ServerPowerServiceTest extends TestCase
{
    private ServerPowerService $service;
    private DaemonPowerRepository $powerRepository;
    private DaemonCommandRepository $commandRepository;

    public function setUp(): void
    {
        parent::setUp();

        $this->powerRepository = m::mock(DaemonPowerRepository::class);
        $this->commandRepository = m::mock(DaemonCommandRepository::class);
        $this->service = new ServerPowerService($this->powerRepository, $this->commandRepository);
    }

    /**
     * Test that stop action triggers save-all before stopping.
     */
    public function testStopActionTriggersaveAllBeforeStop()
    {
        $server = Server::factory()->make(['id' => 1, 'name' => 'test-server']);

        $this->commandRepository->expects('setServer')->with($server)->andReturnSelf();
        $this->commandRepository->expects('send')->with('save-all')->once();

        $this->powerRepository->expects('setServer')->with($server)->andReturnSelf();
        $this->powerRepository->expects('send')->with('stop')->once();

        $this->service->sendPowerAction($server, 'stop');
    }

    /**
     * Test that other actions don't trigger save-all.
     */
    public function testOtherActionsDoNotTriggerSaveAll()
    {
        $server = Server::factory()->make(['id' => 1, 'name' => 'test-server']);

        $this->commandRepository->shouldNotReceive('setServer');
        $this->commandRepository->shouldNotReceive('send');

        $this->powerRepository->expects('setServer')->with($server)->andReturnSelf();
        $this->powerRepository->expects('send')->with('start')->once();

        $this->service->sendPowerAction($server, 'start');
    }

    /**
     * Test that stop still works if save-all fails.
     */
    public function testStopWorksIfSaveAllFails()
    {
        $server = Server::factory()->make(['id' => 1, 'name' => 'test-server']);

        $this->commandRepository->expects('setServer')->with($server)->andReturnSelf();
        $this->commandRepository->expects('send')->with('save-all')
            ->andThrow(new DaemonConnectionException('Connection failed'));

        $this->powerRepository->expects('setServer')->with($server)->andReturnSelf();
        $this->powerRepository->expects('send')->with('stop')->once();

        $this->service->sendPowerAction($server, 'stop');
    }

    /**
     * Test restart action works normally.
     */
    public function testRestartActionWorksNormally()
    {
        $server = Server::factory()->make(['id' => 1, 'name' => 'test-server']);

        $this->commandRepository->shouldNotReceive('setServer');
        $this->commandRepository->shouldNotReceive('send');

        $this->powerRepository->expects('setServer')->with($server)->andReturnSelf();
        $this->powerRepository->expects('send')->with('restart')->once();

        $this->service->sendPowerAction($server, 'restart');
    }

    /**
     * Test kill action works normally.
     */
    public function testKillActionWorksNormally()
    {
        $server = Server::factory()->make(['id' => 1, 'name' => 'test-server']);

        $this->commandRepository->shouldNotReceive('setServer');
        $this->commandRepository->shouldNotReceive('send');

        $this->powerRepository->expects('setServer')->with($server)->andReturnSelf();
        $this->powerRepository->expects('send')->with('kill')->once();

        $this->service->sendPowerAction($server, 'kill');
    }
}
