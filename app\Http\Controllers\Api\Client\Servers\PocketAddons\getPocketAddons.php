<?php

namespace Pterodactyl\Http\Controllers\Api\Client\Servers\PocketAddons;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Request;
use Pterodactyl\Http\Controllers\Controller;
use Pterodactyl\Models\Server;

class getPocketAddons extends Controller
{

    protected $allowed = [
        'Plugins'
    ];

    public function get(Request $request, Server $server)
    {
        RateLimiter::for('pocketaddons', function () {
            return Request::ip();
        });

        RateLimiter::hit('pocketaddons');

        if (RateLimiter::tooManyAttempts('pocketaddons', $perMinute = 30)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Too many requests'
            ], 429);
        }

        $api_response = Http::get('https://pocketaddons.com/api/ressources/');

        if ($api_response->status() != 200) {
            return response()->json([
                'status' => 'error',
                'message' => 'PocketAddons API is not available'
            ], 500);
        }

        $json = $api_response->json();

        $addons = [];
        foreach ($json as $addon) {
            if (in_array($addon['category_name'], $this->allowed)) {
                $addons[] = $addon;
            }
        }

        return response()->json([
            'status' => 'success',
            'data' => $addons
        ]);
    }
}