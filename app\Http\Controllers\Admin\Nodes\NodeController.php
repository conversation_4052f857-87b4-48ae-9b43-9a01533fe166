<?php

namespace Pterodactyl\Http\Controllers\Admin\Nodes;

use Illuminate\View\View;
use Illuminate\Http\Request;
use Pterodactyl\Models\Node;
use Spatie\QueryBuilder\QueryBuilder;
use Pterodactyl\Http\Controllers\Controller;
use Illuminate\Contracts\View\Factory as ViewFactory;
use Illuminate\Http\RedirectResponse;
use Pterodactyl\Http\Requests\Admin\Nodes\StoreNodeRequest;
use Pterodactyl\Http\Requests\Admin\Nodes\UpdateNodeRequest;
use Pterodactyl\Services\Nodes\NodeCreationService;
use Pterodactyl\Services\Nodes\NodeUpdateService;
use Prologue\Alerts\AlertsMessageBag;

class NodeController extends Controller
{
    /**
     * NodeController constructor.
     */
    public function __construct(private ViewFactory $view, private NodeCreationService $creationService, private NodeUpdateService $updateService, private AlertsMessageBag $alert)
    {
    }

    /**
     * Returns a listing of nodes on the system.
     */
    public function index(Request $request): View
    {
        $nodes = QueryBuilder::for(
            Node::query()->with('location')->withCount('servers')
        )
            ->allowedFilters(['uuid', 'name'])
            ->allowedSorts(['id'])
            ->paginate(25);

        return $this->view->make('admin.nodes.index', ['nodes' => $nodes]);
    }

    public function create(StoreNodeRequest $request)
    {
        $node = $this->creationService->handle($request->normalize());
        $this->alert->success(trans('admin/node.notices.node_created'))->flash();

        return RedirectResponse::create('/admin/nodes/view/' . $node->id);
    }

    public function updateNode(UpdateNodeRequest $request, Node $node)
    {
        $this->updateService->handle($node, $request->normalize());
        $this->alert->success(trans('admin/node.notices.node_updated'))->flash();

        return RedirectResponse::create('/admin/nodes/view/' . $node->id);
    }
}
