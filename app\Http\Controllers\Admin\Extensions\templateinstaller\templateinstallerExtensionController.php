<?php

namespace Pterodactyl\Http\Controllers\Admin\Extensions\templateinstaller;

use Illuminate\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\View\Factory as ViewFactory;
use Pterodactyl\Http\Controllers\Controller;
use Pterodactyl\Http\Requests\Admin\AdminFormRequest;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models\Template;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models\TemplateStep;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models\TemplateVariable;
use Pterodactyl\BlueprintFramework\Libraries\ExtensionLibrary\Admin\BlueprintAdminLibrary;

class templateinstallerPostFormRequest extends AdminFormRequest
{
    public function rules(): array
    {
        return [
            'type' => 'required|string',
        ];
    }
}

class templateinstallerExtensionController extends Controller
{
    private $variables = [
        '$server[\'id\']' => 'The ID of the server.',
        '$server[\'uuid\']' => 'The UUID of the server.',
        '$server[\'name\']' => 'The name of the server.',
        '$server[\'node\'][\'id\']' => 'The ID of the node the server is on.',
        '$server[\'node\'][\'name\']' => 'The name of the node the server is on.',
        '$server[\'user\'][\'id\']' => 'The ID of the user that owns the server.',
        '$server[\'user\'][\'username\']' => 'The username of the user that owns the server.',
        '$server[\'user\'][\'email\']' => 'The email of the user that owns the server.',
        '$server[\'allocations\']' => 'An array of allocations for the server. First allocation is the primary allocation.',
        '$server[\'allocations\'][0][\'id\']' => 'The ID of the allocation.',
        '$server[\'allocations\'][0][\'ip\']' => 'The IP of the allocation.',
        '$server[\'allocations\'][0][\'alias\']' => 'The IP alias of the allocation.',
        '$server[\'allocations\'][0][\'port\']' => 'The port of the allocation.',
        '$server[\'allocations\'][...][...]' => 'Continue this pattern for additional allocations.',
        '$egg[\'id\']' => 'The ID of the egg the server is using.',
        '$egg[\'name\']' => 'The name of the egg the server is using.',
        '$egg[\'startup\']' => 'The startup command of the egg the server is using.',
        '$nest[\'id\']' => 'The ID of the nest the server is in.',
        '$nest[\'name\']' => 'The name of the nest the server is in.',
        '$request(...args)' => 'Call a Guzzle http client for any external requests.',
        '$follow_redirects(url)' => 'Follow redirects for the given URL. Returns the final URL.',
    ];

    private $allowedTags = [
        '<a>',
        '<b>',
        '<blockquote>',
        '<code>',
        '<del>',
        '<dd>',
        '<dl>',
        '<dt>',
        '<em>',
        '<h1>',
        '<h2>',
        '<h3>',
        '<h4>',
        '<h5>',
        '<h6>',
        '<li>',
        '<ol>',
        '<p>',
        '<pre>',
        '<s>',
        '<sup>',
        '<sub>',
        '<strong>',
        '<strike>',
        '<ul>',
    ];

    private $actions = [
        'write' => ['file' => 'required|string'],
        'replace' => ['file' => 'required|string', 'search' => 'required|string'],
        'append' => ['file' => 'required|string'],
        'prepend' => ['file' => 'required|string'],
        'mkdir' => ['folder' => 'required|string'],
        'pull' => ['file' => 'required|string'],
        'unzip' => ['file' => 'required|string', 'destination' => 'required|string'],
        'move' => ['file' => 'required|string', 'destination' => 'required|string'],
        'move-content' => ['folder' => 'required|string', 'destination' => 'required|string'],
        'delete' => ['file' => 'required|string'],
        'power-action' => ['state' => 'required|string'],
    ];

    public function __construct(
        private ViewFactory $view,
        private BlueprintAdminLibrary $blueprint,
    ) {}

    public function index(): View
    {
        $queryParameters = request()->query();
        $template = null;
        $variable = null;
        $step = null;

        if (isset($queryParameters['template'])) {
            $template = Template::query()->where('id', $queryParameters['template'])->first();
        }

        if (isset($queryParameters['variable'])) {
            $variable = TemplateVariable::query()->where('id', $queryParameters['variable'])->first();
        }

        if (isset($queryParameters['step'])) {
            $step = TemplateStep::query()->where('id', $queryParameters['step'])->first();
        }

        return $this->view->make(
            'admin.extensions.templateinstaller.index', [
                'root' => '/admin/extensions/templateinstaller',
                'blueprint' => $this->blueprint,
                'viewing' => isset($queryParameters['viewing']) ? $queryParameters['viewing'] : 'templates',
                'templates' => Template::query()->orderBy('order', 'asc')->orderBy('id', 'asc')->get(['id', 'order', 'icon', 'name', 'category', 'version', 'author']),
                'template' => $template,
                'variable' => $variable,
                'step' => $step,
                'default_variables' => $this->variables,
            ]
        );
    }

    public function post(templateinstallerPostFormRequest $request): View
    {
        $type = $request->input('type');

        switch ($type) {
            case 'template-new':
                $data = $this->validate($request, [
                    'order' => 'required|integer',
                    'name' => 'required|string|max:191',
                    'icon' => 'nullable|string|max:191',
                    'category' => 'nullable|string|max:191',
                    'description' => 'nullable|string|max:191',
                    'full_description' => 'nullable|string',
                    'version' => 'required|string|max:191',
                    'author' => 'required|string|max:191',
                    'startup_command' => 'nullable|string',
                    'selectable' => 'required|boolean',
                    'force_reinstall' => 'required|boolean',
                    'force_wipe' => 'required|boolean',
                    'eggs' => 'required|array',
                    'eggs.*' => 'required|integer|exists:eggs,id',
                ]);

                $data['description'] = !$data['description'] ? null : strip_tags($data['description'], $this->allowedTags);

                Template::create($data);

                $this->blueprint->notify('Template successfully created.');
                break;

            case 'template-edit':
                $data = $this->validate($request, [
                    'id' => 'required|integer|exists:templates,id',
                    'order' => 'required|integer',
                    'name' => 'required|string|max:191',
                    'icon' => 'nullable|string|max:191',
                    'category' => 'nullable|string|max:191',
                    'description' => 'nullable|string|max:191',
                    'full_description' => 'nullable|string',
                    'version' => 'required|string|max:191',
                    'author' => 'required|string|max:191',
                    'startup_command' => 'nullable|string',
                    'selectable' => 'required|boolean',
                    'force_reinstall' => 'required|boolean',
                    'force_wipe' => 'required|boolean',
                    'eggs' => 'required|array',
                    'eggs.*' => 'required|integer|exists:eggs,id',
                ]);

                $data['description'] = !$data['description'] ? null : strip_tags($data['description'], $this->allowedTags);

                Template::query()->where('id', $data['id'])->update($data);

                $this->blueprint->notify('Template successfully updated.');
                break;

            case 'template-import':
                $data = json_decode($request->input('data'), true);

                try {
                    $template = [
                        'order' => $data['order'],
                        'name' => $data['name'],
                        'icon' => $data['icon'],
                        'category' => $data['category'],
                        'description' => $data['description'],
                        'full_description' => $data['full_description'],
                        'startup_command' => $data['startup_command'],
                        'version' => $data['version'],
                        'author' => $data['author'],
                        'selectable' => $data['selectable'],
                        'force_reinstall' => $data['force_reinstall'],
                        'force_wipe' => $data['force_wipe'],
                        'eggs' => $data['eggs'],
                    ];

                    /** @var Template $template */
                    $template = Template::create($template);

                    foreach ($data['variables'] as $variable) {
                        TemplateVariable::create([
                            'template_id' => $template->id,
                            ...$variable,
                        ]);
                    }

                    foreach ($data['steps'] as $step) {
                        TemplateStep::create([
                            ...$step,
                            'template_id' => $template->id,
                            'metadata' => json_decode($step['metadata'], true),
                        ]);
                    }

                    $this->blueprint->notify('Template successfully imported.');
                } catch (\Exception $exception) {
                    $this->blueprint->notify("An error occurred while importing the template.\n{$exception->getMessage()}");
                }

                break;

            case 'variable-new':
                $data = $this->validate($request, [
                    'template_id' => 'required|integer|exists:templates,id',
                    'order' => 'required|integer',
                    'name' => 'required|string|max:191',
                    'variable' => 'required|string|max:191|regex:/^[a-zA-Z_][a-zA-Z0-9_]*$/|not_in:server,egg,nest,request,follow_redirects',
                    'description' => 'nullable|string|max:191',
                    'default_value' => 'nullable|string|max:191',
                    'rules' => 'nullable|string|max:191',
                    'selectable' => 'required|boolean',
                ]);

                TemplateVariable::create($data);

                $this->blueprint->notify('Variable successfully created.');
                break;

            case 'variable-edit':
                $data = $this->validate($request, [
                    'id' => 'required|integer|exists:template_variables,id',
                    'template_id' => 'required|integer|exists:templates,id',
                    'order' => 'required|integer',
                    'name' => 'required|string|max:191',
                    'variable' => 'required|string|max:191|regex:/^[a-zA-Z_][a-zA-Z0-9_]*$/|not_in:server,egg,nest,request,follow_redirects',
                    'description' => 'nullable|string|max:191',
                    'default_value' => 'nullable|string|max:191',
                    'rules' => 'nullable|string|max:191',
                    'selectable' => 'required|boolean',
                ]);

                TemplateVariable::query()->where('id', $data['id'])->update($data);

                $this->blueprint->notify('Variable successfully updated.');
                break;

            case 'step-new':
                $data = $this->validate($request, [
                    'template_id' => 'required|integer|exists:templates,id',
                    'order' => 'required|integer',
                    'action' => 'required|string|in:' . implode(',', array_keys($this->actions)),
                    'metadata' => 'required|string',
                    'content' => 'required|string',
                ]);

                $metadata = Validator::make(json_decode($data['metadata'], true), $this->actions[$data['action']])->validate();
                $data['metadata'] = $metadata;

                TemplateStep::create($data);

                $this->blueprint->notify('Step successfully created.');
                break;

            case 'step-edit':
                $data = $this->validate($request, [
                    'id' => 'required|integer|exists:template_steps,id',
                    'template_id' => 'required|integer|exists:templates,id',
                    'order' => 'required|integer',
                    'action' => 'required|string|in:' . implode(',', array_keys($this->actions)),
                    'metadata' => 'required|string',
                    'content' => 'required|string',
                ]);

                $metadata = Validator::make(json_decode($data['metadata'], true), $this->actions[$data['action']])->validate();
                $data['metadata'] = $metadata;

                TemplateStep::query()->where('id', $data['id'])->update($data);

                $this->blueprint->notify('Step successfully updated.');
                break;
        }

        return $this->index();
    }

    public function delete(Request $request): View
    {
        $target = $request->route('target');
        $id = $request->route('id');

        switch ($target) {
            case 'template':
                Template::query()->where('id', $id)->delete();

                $this->blueprint->notify('Template successfully deleted.');
                break;

            case 'variable':
                $templateVariable = TemplateVariable::query()->where('id', $id)->first();
                $templateVariable->delete();

                $this->blueprint->notify('Variable successfully deleted.');
                break;

            case 'step':
                $templateStep = TemplateStep::query()->where('id', $id)->first();
                $templateStep->delete();

                $this->blueprint->notify('Step successfully deleted.');
                break;
        }

        return $this->index();
    }
}