<?php

namespace Pterodactyl\Http\Controllers\Admin\MyPlugins;

use Pterodactyl\Http\Controllers\Controller;
use Pterodactyl\Models\BackupToKeep;
use Pterodactyl\Models\DeletingServers;
use Illuminate\Http\Request;
use Prologue\Alerts\AlertsMessageBag;

class BackupController extends Controller
{

    protected $alert;
    public function __construct(AlertsMessageBag $alert)
    {
        $this->alert = $alert;
    }

    public function index(Request $request)
    {
        $offset = $request->input('offset', 0);
        if ($offset < 0) {
            $offset = 0;
            header('Location: ' . route('admin.myplugins.backups', ['offset' => $offset]));
        }
        $search = $request->input('search', null);

        $backups = BackupToKeep::query()
            ->where('delete_at', '>', now())
            ->when($search, function ($query, $search) {
                return $query->where('server_owner_email', 'like', '%' . $search . '%');
            })
            ->orderBy('created_at', 'desc')
            ->offset($offset)
            ->limit(50)
            ->get();
        $pending_detleting_servers = DeletingServers::query()->get();
        $retentions = \DB::table('backup_config')->first()->retentions;

        return view('admin.myplugins.backups.index', [
            'backups' => $backups,
            'pending_detleting_servers' => $pending_detleting_servers,
            'retentions' => $retentions ?? 365,
            'offset' => $offset,
            'search' => $search
        ]);
    }

    public function update(Request $request)
    {
        // store in config the new retentions
        $retentions = $request->input('retentions', 365);
        if ($retentions <= 0) {
            $retentions = 365;
        }

        \DB::table('backup_config')->update([
            'retentions' => $retentions
        ]);

        $this->alert->success('Backup retentions have been updated.')->flash();

        return redirect()->route('admin.myplugins.backups');
    }

    public function delete(Request $request)
    {
        $backup_uuid = $request->input('backup_uuid', null);
        if ($backup_uuid == null) {
            $this->alert->danger('Backup ID is missing.')->flash();
            return redirect()->route('admin.myplugins.backups');
        }

        $backup = BackupToKeep::query()->where('backup_uuid', $backup_uuid)->first();
        if ($backup == null) {
            $this->alert->danger('Backup not found.')->flash();
            return redirect()->route('admin.myplugins.backups');
        }

        $backup->update([
            'delete_at' => time()
        ]);

        $this->alert->success('Backup will be deleted in the next cronjob.')->flash();

        return redirect()->route('admin.myplugins.backups');
    }
}