<?php

namespace Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models;

use Pterodactyl\Models\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * TemplateStep.
 *
 * @property int $id
 * @property int $order
 * @property int $template_id
 * @property string $content
 * @property string $action
 * @property array $metadata
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models\Template $template
 *
 * @method static \Database\Factories\TemplateStep factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|TemplateStep newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TemplateStep newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TemplateStep query()
 * @method static \Illuminate\Database\Eloquent\Builder|TemplateStep whereId($value)
 *
 * @mixin \Eloquent
 */
class TemplateStep extends Model
{
    public const CREATED_AT = null;
    public const UPDATED_AT = null;

    /**
     * The resource name for this model when it is transformed into an
     * API representation using fractal.
     */
    public const RESOURCE_NAME = 'template_step';

    /**
     * The table associated with the model.
     */
    protected $table = 'template_steps';

    /**
     * Cast values to correct type.
     */
    protected $casts = [
        'metadata' => 'array',
    ];

    /**
     * Fields that are mass assignable.
     */
    protected $fillable = [
        'order',
        'template_id',
        'content',
        'action',
        'metadata',
    ];

    /**
     * Rules to protect against invalid data entry to DB.
     */
    public static array $validationRules = [
        'order' => 'required|integer',
        'template_id' => 'required|exists:templates,id',
        'content' => 'required|string',
        'action' => 'required|string',
        'metadata' => 'array',
    ];

    /**
     * Returns the template this variable is assigned to.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class, 'template_id');
    }
}
