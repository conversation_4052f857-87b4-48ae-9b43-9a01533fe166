<?php

namespace Pterodactyl\Http\Controllers\Api\Client\Servers;

use Illuminate\Http\Request;
use Pterodactyl\Models\Server;
use Pterodactyl\Models\Permission;
use Pterodactyl\Http\Controllers\Controller;
use Pterodactyl\Services\Ai\LogAnalysisService;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Pterodactyl\Http\Requests\Api\Client\ClientApiRequest;

class LogAnalysisController extends Controller
{
    /**
     * @var \Pterodactyl\Services\Ai\LogAnalysisService
     */
    private $logAnalysisService;

    /**
     * LogAnalysisController constructor.
     */
    public function __construct(LogAnalysisService $logAnalysisService)
    {
        $this->logAnalysisService = $logAnalysisService;
    }

    /**
     * Handles the request to analyze server logs using AI.
     *
     * @param \Pterodactyl\Http\Requests\Api\Client\ClientApiRequest $request
     * @param \Pterodactyl\Models\Server $server
     * @return \Illuminate\Http\JsonResponse
     * 
     * @throws \Symfony\Component\HttpKernel\Exception\BadRequestHttpException
     */
    public function __invoke(ClientApiRequest $request, Server $server)
    {
        $this->authorize(Permission::ACTION_WEBSOCKET_CONNECT, $server);

        if (!config('ai.log_analysis.enabled')) {
            return response()->json([
                'success' => false,
                'message' => 'AI log analysis is not enabled on this system.',
            ], 503);
        }

        $request->validate([
            'log' => 'required|string|min:10',
        ]);

        $logContent = $request->input('log');

        // Analyze the log content
        $result = $this->logAnalysisService->analyze($logContent);

        if (!$result['success']) {
            throw new BadRequestHttpException($result['message'] ?? 'Could not analyze logs.');
        }

        return response()->json([
            'success' => true,
            'data' => $result['analysis'],
        ]);
    }
} 