<?php

namespace Pterodactyl\Http\Controllers\Api\Client\Servers;

use Illuminate\Http\Request;
use Pterodactyl\Models\Server;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Pterodactyl\Http\Controllers\Api\Client\ClientApiController;

class McStatusController extends ClientApiController
{
    /**
     * Proxy pour l'API mcsrvstat.us pour éviter les problèmes CORS.
     * Cette méthode récupère le statut d'un serveur Minecraft depuis l'API mcsrvstat.us
     * et le renvoie au client.
     *
     * @param Request $request
     * @param Server $server
     * @return JsonResponse
     */
    public function proxyMcStatus(Request $request, Server $server): JsonResponse
    {
        // Récupérer les paramètres de la requête
        $ip = $request->query('ip');
        $port = $request->query('port', '25565');

        if (empty($ip)) {
            return new JsonResponse([
                'error' => 'L\'adresse IP est requise',
            ], 400);
        }

        try {
            // Journaliser le début de l'appel
            Log::info('Début de l\'appel à l\'API mcsrvstat.us', [
                'ip' => $ip,
                'port' => $port,
                'server_id' => $server->id,
                'server_uuid' => $server->uuid,
            ]);

            // Appeler l'API mcsrvstat.us
            $response = Http::withHeaders([
                'User-Agent' => 'GameHostingVN-Panel/1.0',
                'Accept' => 'application/json',
            ])->timeout(15)->get("https://api.mcsrvstat.us/3/{$ip}:{$port}");

            // Journaliser la réponse
            Log::info('Réponse de l\'API mcsrvstat.us', [
                'status' => $response->status(),
                'body_length' => strlen($response->body()),
                'ip' => $ip,
                'port' => $port,
            ]);

            // Vérifier si la requête a réussi
            if ($response->successful()) {
                $data = $response->json();

                // Vérifier si la réponse contient la clé 'online'
                if (isset($data['online'])) {
                    return new JsonResponse($data);
                }

                // Si la réponse ne contient pas la clé 'online', c'est probablement une erreur
                Log::warning('Réponse de l\'API mcsrvstat.us sans clé online', [
                    'data' => $data,
                    'ip' => $ip,
                    'port' => $port,
                ]);

                return new JsonResponse([
                    'online' => false,
                    'error' => 'Réponse de l\'API mcsrvstat.us sans clé online',
                    'raw_data' => $data,
                ]);
            }

            // Gérer les erreurs de l'API
            Log::error('Erreur lors de l\'appel à l\'API mcsrvstat.us', [
                'status' => $response->status(),
                'body' => $response->body(),
                'ip' => $ip,
                'port' => $port,
            ]);

            return new JsonResponse([
                'online' => false,
                'error' => 'Erreur lors de l\'appel à l\'API mcsrvstat.us',
                'status' => $response->status(),
            ]);
        } catch (\Exception $e) {
            // Gérer les exceptions
            Log::error('Exception lors de l\'appel à l\'API mcsrvstat.us', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $ip,
                'port' => $port,
            ]);

            return new JsonResponse([
                'online' => false,
                'error' => 'Erreur lors de l\'appel à l\'API mcsrvstat.us: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Endpoint simple pour vérifier si un serveur est en ligne.
     * Cette méthode utilise l'API simple de mcsrvstat.us qui renvoie un code HTTP 200 si le serveur est en ligne
     * et un code HTTP 404 si le serveur est hors ligne.
     *
     * @param Request $request
     * @param Server $server
     * @return JsonResponse
     */
    public function proxyMcStatusSimple(Request $request, Server $server): JsonResponse
    {
        // Récupérer les paramètres de la requête
        $ip = $request->query('ip');
        $port = $request->query('port', '25565');

        if (empty($ip)) {
            return new JsonResponse([
                'error' => 'L\'adresse IP est requise',
            ], 400);
        }

        try {
            // Journaliser le début de l'appel
            Log::info('Début de l\'appel à l\'API simple de mcsrvstat.us', [
                'ip' => $ip,
                'port' => $port,
                'server_id' => $server->id,
                'server_uuid' => $server->uuid,
            ]);

            // Appeler l'API simple de mcsrvstat.us
            $response = Http::withHeaders([
                'User-Agent' => 'GameHostingVN-Panel/1.0',
                'Accept' => 'application/json',
            ])->timeout(10)->get("https://api.mcsrvstat.us/simple/{$ip}:{$port}");

            // Journaliser la réponse
            Log::info('Réponse de l\'API simple de mcsrvstat.us', [
                'status' => $response->status(),
                'body_length' => strlen($response->body()),
                'ip' => $ip,
                'port' => $port,
            ]);

            // Renvoyer le statut du serveur
            return new JsonResponse([
                'online' => $response->status() === 200,
                'status_code' => $response->status(),
            ]);
        } catch (\Exception $e) {
            // Gérer les exceptions
            Log::error('Exception lors de l\'appel à l\'API simple de mcsrvstat.us', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $ip,
                'port' => $port,
            ]);

            return new JsonResponse([
                'online' => false,
                'error' => 'Erreur lors de l\'appel à l\'API simple de mcsrvstat.us: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Méthode simplifiée pour vérifier si un serveur est accessible depuis l'extérieur.
     * Cette méthode utilise une vérification directe de la connectivité TCP.
     *
     * @param Request $request
     * @param Server $server
     * @return JsonResponse
     */
    public function checkInternationalBlock(Request $request, Server $server): JsonResponse
    {
        // Récupérer les paramètres de la requête
        $ip = $request->query('ip');
        $port = $request->query('port', '25565');

        if (empty($ip)) {
            return new JsonResponse([
                'error' => 'L\'adresse IP est requise',
            ], 400);
        }

        // Clé de cache unique pour cette vérification
        $cacheKey = "international_block_check_{$ip}_{$port}";

        // Vérifier si le résultat est en cache (valide pendant 5 minutes)
        if (Cache::has($cacheKey)) {
            return new JsonResponse(Cache::get($cacheKey));
        }

        try {
            // Journaliser le début de la vérification
            Log::info('Début de la vérification de blocage international avec check-host.net', [
                'ip' => $ip,
                'port' => $port,
                'server_id' => $server->id,
                'server_uuid' => $server->uuid,
            ]);

            // Vérifier si le serveur est accessible localement
            $locallyAccessible = $this->isServerAccessible($ip, $port);

            Log::info('Résultat de la vérification locale', [
                'ip' => $ip,
                'port' => $port,
                'locally_accessible' => $locallyAccessible,
            ]);

            // Étape 1: Initier la vérification TCP avec check-host.net
            $checkHostResponse = Http::withHeaders([
                'Accept' => 'application/json',
            ])->get("https://check-host.net/check-tcp?host={$ip}:{$port}&max_nodes=1");

            if (!$checkHostResponse->successful()) {
                Log::error('Erreur lors de l\'appel à check-host.net', [
                    'status' => $checkHostResponse->status(),
                    'body' => $checkHostResponse->body(),
                ]);

                $result = [
                    'blocked' => false, // Par défaut, nous supposons que le serveur n'est pas bloqué
                    'message' => 'Không thể kiểm tra kết nối quốc tế (lỗi API)',
                    'local_check' => $locallyAccessible,
                    'international_check' => null,
                ];

                Cache::put($cacheKey, $result, 300);
                return new JsonResponse($result);
            }

            $checkData = $checkHostResponse->json();

            if (!isset($checkData['request_id'])) {
                Log::error('Réponse invalide de check-host.net', [
                    'response' => $checkData,
                ]);

                $result = [
                    'blocked' => false,
                    'message' => 'Không thể kiểm tra kết nối quốc tế (phản hồi không hợp lệ)',
                    'local_check' => $locallyAccessible,
                    'international_check' => null,
                ];

                Cache::put($cacheKey, $result, 300);
                return new JsonResponse($result);
            }

            $requestId = $checkData['request_id'];
            $permanentLink = $checkData['permanent_link'] ?? null;

            // Attendre un peu pour que la vérification soit effectuée
            sleep(3);

            // Étape 2: Récupérer les résultats de la vérification
            $resultResponse = Http::withHeaders([
                'Accept' => 'application/json',
            ])->get("https://check-host.net/check-result/{$requestId}");

            if (!$resultResponse->successful()) {
                Log::error('Erreur lors de la récupération des résultats de check-host.net', [
                    'status' => $resultResponse->status(),
                    'body' => $resultResponse->body(),
                ]);

                $result = [
                    'blocked' => false,
                    'message' => 'Không thể kiểm tra kết nối quốc tế (lỗi khi lấy kết quả)',
                    'local_check' => $locallyAccessible,
                    'international_check' => null,
                    'permanent_link' => $permanentLink,
                ];

                Cache::put($cacheKey, $result, 300);
                return new JsonResponse($result);
            }

            $resultData = $resultResponse->json();

            // Analyser les résultats
            $internationallyAccessible = false;
            $nodeResults = [];

            foreach ($resultData as $node => $results) {
                if (is_array($results) && !empty($results)) {
                    foreach ($results as $result) {
                        if (isset($result['time'])) {
                            // Si au moins un nœud peut se connecter, le serveur est accessible internationalement
                            $internationallyAccessible = true;
                            $nodeResults[$node] = [
                                'status' => 'success',
                                'time' => $result['time'],
                            ];
                        } elseif (isset($result['error'])) {
                            $nodeResults[$node] = [
                                'status' => 'error',
                                'error' => $result['error'],
                            ];
                        }
                    }
                } elseif ($results === null) {
                    $nodeResults[$node] = [
                        'status' => 'pending',
                    ];
                }
            }

            // Si le serveur est accessible localement mais pas internationalement, il est bloqué
            $isBlocked = $locallyAccessible && !$internationallyAccessible;

            $result = [
                'blocked' => $isBlocked,
                'message' => $isBlocked
                    ? 'Server bị chặn quốc tế'
                    : 'Server không bị chặn quốc tế',
                'local_check' => $locallyAccessible,
                'international_check' => $internationallyAccessible,
                'node_results' => $nodeResults,
                'permanent_link' => $permanentLink,
            ];

            // Mettre en cache le résultat pendant 5 minutes
            Cache::put($cacheKey, $result, 300);

            return new JsonResponse($result);
        } catch (\Exception $e) {
            // Gérer les exceptions
            Log::error('Exception lors de la vérification de blocage international', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $ip,
                'port' => $port,
            ]);

            return new JsonResponse([
                'blocked' => false, // Par défaut, nous supposons que le serveur n'est pas bloqué en cas d'erreur
                'error' => 'Lỗi khi kiểm tra block quốc tế: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Vérifie si un serveur est accessible localement.
     *
     * @param string $ip
     * @param int $port
     * @return bool
     */
    private function isServerAccessible(string $ip, int $port): bool
    {
        try {
            $socket = @fsockopen($ip, $port, $errno, $errstr, 2);

            if ($socket) {
                fclose($socket);
                return true;
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }
}
