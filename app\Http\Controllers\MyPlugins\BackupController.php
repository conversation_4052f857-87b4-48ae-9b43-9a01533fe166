<?php

namespace Pterodactyl\Http\Controllers\MyPlugins;

use Illuminate\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\Factory as ViewFactory;
use Pterodactyl\Http\Controllers\Controller;
use Pterodactyl\Contracts\Repository\ServerRepositoryInterface;
use Pterodactyl\Models\Backup;
use Pterodactyl\Models\BackupToKeep;
use Pterodactyl\Services\Backups\DownloadLinkService;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class BackupController extends Controller
{
    /**
     * IndexController constructor.
     */
    public function __construct(
        protected ServerRepositoryInterface $repository,
        protected ViewFactory $view,
        private DownloadLinkService $downloadLinkService,
    ) {
    }

    /**
     * Download the backup for a given server instance. For daemon local files, the file
     * will be streamed back through the Panel. For AWS S3 files, a signed URL will be generated
     * which the user is redirected to.
     *
     * @throws \Throwable
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function download(Request $request)
    {
        $server = $request->input('server');
        $backup = $request->input('backup');
        if (is_null($server) || is_null($backup)) {
            return new JsonResponse([
                'object' => 'error',
                'message' => 'Thiếu tham số máy chủ hoặc sao lưu.'
            ], 400);
        }

        $backup = BackupToKeep::query()->where('server_uuid', $server)->where('backup_uuid', $backup)->first();

        if (is_null($backup) || $backup->server_owner != $request->user()->id && !$request->user()->root_admin && !$request->user()->root_admin) {
            return new JsonResponse([
                'object' => 'error',
                'message' => 'Không tìm thấy bản sao lưu. Vui lòng làm mới trang và thử lại..',
            ], 404);
        }

        if ($backup->disk !== Backup::ADAPTER_AWS_S3 && $backup->disk !== Backup::ADAPTER_WINGS) {
            throw new BadRequestHttpException('Bản sao lưu được yêu cầu tham chiếu đến một loại trình điều khiển đĩa không xác định và không thể tải xuống.');
        }

        $url = $this->downloadLinkService->download($backup, $request->user());

        header('Location: ' . $url);
    }

    public function status(Request $request): JsonResponse
    {
        $user = $request->user();
        $admin_active = $request->input('admin_active');
        if ($admin_active && !$user->root_admin) {
            return new JsonResponse([
                'object' => 'error',
                'message' => 'Bạn không có quyền thực hiện hành động này..'
            ], 403);
        }

        if (!$admin_active) {
            $backups = BackupToKeep::query()->where('server_owner', $user->id)->get();
        } else {
            $backups = BackupToKeep::query()->get();
        }

        $backups = $backups->map(function ($backup) {
            return [
                'server' => $backup->server_uuid,
                'backup' => $backup->backup_uuid,
                'delete_at' => $backup->delete_at,
                'server_name' => $backup->server_name,
                'backup_name' => $backup->backup_name,
                'size' => $backup->size,
            ];
        });
        return new JsonResponse([
            'object' => 'list',
            'data' => $backups
        ]);
    }
}
