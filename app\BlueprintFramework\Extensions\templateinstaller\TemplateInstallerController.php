<?php

namespace Pterodactyl\BlueprintFramework\Extensions\templateinstaller;

use GuzzleHttp\Client;
use Pterodactyl\Models\Server;
use Illuminate\Http\JsonResponse;
use Pterodactyl\Facades\Activity;
use Illuminate\Support\Facades\Log;
use Pterodactyl\Services\Servers\ReinstallServerService;
use Pterodactyl\Repositories\Wings\DaemonFileRepository;
use Pterodactyl\Repositories\Wings\DaemonPowerRepository;
use Pterodactyl\Http\Controllers\Api\Client\ClientApiController;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models\Template;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models\TemplateStep;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models\TemplateVariable;
use Pterodactyl\BlueprintFramework\Libraries\ExtensionLibrary\Client\BlueprintClientLibrary;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Transformers\TemplateTransformer;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Requests\TemplateInstallerIndexRequest;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Requests\TemplateInstallerInstallRequest;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Transformers\TemplateVariableTransformer;

class SecureExpressionEvaluator
{
    private $allowedFunctions = [
        'json_encode',
        'json_decode',
        'str_replace',
        'preg_replace',
        'strtolower',
        'strtoupper',
        'implode',
        'explode',
        'trim',
        'ltrim',
        'rtrim',
        'substr',
        'strlen',
        'count',
        'array_merge',
        'array_map',
        'array_filter',
        'array_keys',
        'array_values',
        'array_flip',
        'array_reverse',
        'array_unique',
        'str_starts_with',
        'str_ends_with',
        'str_contains',
        'str_replace',
        'str_repeat',
        'str_pad',
        'str_split',
        'str_shuffle',
        'str_word_count',
        'strip_tags',
        'fn',
        'end',
        'compact',
        'parse_url',
    ];

    public function __construct()
    {
        $this->allowedFunctions = array_flip($this->allowedFunctions);
    }

    private function followRedirects($url, $max = 10, $used = 0, $prev = null) {
        if ($used >= $max) {
            return $url;
        }

        if (str_starts_with($url, '/')) {
            $host = parse_url($prev, PHP_URL_HOST);

            if (!$host) {
                throw new \Exception('Failed to determine host.');
            }

            $url = sprintf('%s://%s%s', parse_url($prev, PHP_URL_SCHEME), $host, $url);
        }

        $response = get_headers($url, true);

        if (!$response) {
            throw new \Exception('Failed to query URL.');
        }

        $response = array_change_key_case($response, CASE_LOWER);

        if (array_key_exists('location', $response)) {
            try {
                if (is_array($response['location'])) {
                    return $this->followRedirects($response['location'][count($response['location']) - 1], $max, $used + 1, $url);
                } else {
                    return $this->followRedirects($response['location'], $max, $used + 1, $url);
                }
            } catch (\Exception $e) {
                return $url;
            }
        }

        return $url;
    }

    public function evaluate(string $expression, array $variables)
    {
        $allowedFunctions = $this->allowedFunctions;

        $request = function(...$args) {
            $client = new Client();

            return $client->request(...$args)->getBody()->getContents();
        };

        $follow_redirects = function(string $url, int $max = 10, int $used = 0, string|null $prev = null) {
            return $this->followRedirects($url, $max, $used, $prev);
        };

        $evaluator = function($expr) use ($variables, $allowedFunctions, $request, $follow_redirects) {
            extract($variables);
            extract(compact('request'));
            extract(compact('follow_redirects'));

            $allowedFunctions['request'] = true;
            $allowedFunctions['follow_redirects'] = true;

            // Replace function calls
            $expr = preg_replace_callback('/(\w+)\s*\(/', function($matches) use ($allowedFunctions) {
                return array_key_exists($matches[1], $allowedFunctions) ? $matches[0] : 'null';
            }, $expr);

            // Replace string function calls (e.g. 'foo'(), ('foo')(), etc.)
            $expr = preg_replace('/(\'|")\w+\1\s*\)?\s*\(/', 'null', $expr);

            // Remove object or static method calls
            $expr = preg_replace('/\$?\w+\s*->\s*\w+\s*\(/', 'null', $expr);
            $expr = preg_replace('/\w+\s*::\s*\w+\s*\(/', 'null', $expr);

            return eval('return ' . $expr . ';');
        };

        return $evaluator($expression);
    }

    public function processPlaceholders(string $content, array $variables): string
    {
        return preg_replace_callback('/\{\{(.+?)\}\}/', function ($matches) use ($variables) {
            try {
                $expression = trim($matches[1]);

                $result = $this->evaluate($expression, $variables);
                if (is_array($result) || is_object($result)) {
                    $result = json_encode($result);
                }

                return $result;
            } catch (\Throwable $e) {
                Log::error($e->getMessage(), [
                    'where' => 'templates.placeholder-injection',
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString(),
                ]);

                return $matches[0];
            }
        }, $content);
    }
}

class TemplateInstallerController extends ClientApiController
{
    public function __construct(
        private DaemonFileRepository $fileRepository,
        private DaemonPowerRepository $powerRepository,
        private ReinstallServerService $reinstallService,
        private BlueprintClientLibrary $blueprint,
        private SecureExpressionEvaluator $evaluator,
    ) {
        parent::__construct();
    }

    private function getRelativePath(string $filePath, string $destination): string {
        if ($destination === '/') {
            return $filePath;
        }

        // Clean and normalize paths
        $filePath = str_replace('\\', '/', $filePath);
        $destination = str_replace('\\', '/', $destination);
        
        // Split paths into arrays
        $filePathParts = explode('/', trim($filePath, '/'));
        $destParts = explode('/', trim($destination, '/'));
        
        // Find common path parts
        $i = 0;
        $commonLength = 0;
        while ($i < min(count($filePathParts), count($destParts))) {
            if ($filePathParts[$i] === $destParts[$i]) {
                $commonLength++;
            } else {
                break;
            }
            $i++;
        }
        
        // Calculate levels to go up
        $upCount = count($destParts) - $commonLength;
        
        // Build relative path
        $relativePath = str_repeat('../', $upCount);
        
        // Add remaining file path parts
        $remainingParts = array_slice($filePathParts, $commonLength);
        $relativePath .= implode('/', $remainingParts);
        
        return $relativePath;
    }

    private function n(string $str): string {
        return str_replace('\n', "\n", $str);
    }

    private function handleStep(TemplateStep $step, array $variables, Server $server, int $templateId): void
    {
        $content = $this->n($this->evaluator->processPlaceholders($step->content, $variables));
        $metadata = $step->metadata;

        try {
            switch ($step->action) {
                case 'write':
                    $file = $this->evaluator->processPlaceholders($metadata['file'], $variables);
                    $this->fileRepository->setServer($server)->putContent($file, $content);

                    break;

                case 'mkdir':
                    $folder = $this->evaluator->processPlaceholders($metadata['folder'], $variables);
                    $this->fileRepository->setServer($server)->createDirectory($folder, '/');

                    break;

                case 'pull':
                    $file = $this->evaluator->processPlaceholders($metadata['file'], $variables);

                    if (!filter_var($content, FILTER_VALIDATE_URL)) {
                        return;
                    }

                    $this->fileRepository->setServer($server)->pull(trim($content), '/', [
                        'foreground' => true,
                        'filename' => $file,
                        'use_header' => false,
                    ]);

                    break;

                case 'unzip':
                    $file = $this->evaluator->processPlaceholders($metadata['file'], $variables);
                    $destination = $this->evaluator->processPlaceholders($metadata['destination'], $variables);

                    if (!$file || !$destination) {
                        return;
                    }

                    $fileRelativeToDestination = $this->getRelativePath($file, $destination);

                    $this->fileRepository->setServer($server)->decompressFile($destination, $fileRelativeToDestination);

                    break;

                case 'move':
                    $file = $this->evaluator->processPlaceholders($metadata['file'], $variables);
                    $destination = $this->evaluator->processPlaceholders($metadata['destination'], $variables);

                    if (!$file || !$destination) {
                        return;
                    }

                    $this->fileRepository->setServer($server)->deleteFiles(dirname($destination), [basename($destination)]);
                    $this->fileRepository->setServer($server)->renameFiles('/', [[
                        'from' => $file,
                        'to' => $destination,
                    ]]);

                    break;

                case 'move-content':
                    $folder = $this->evaluator->processPlaceholders($metadata['folder'], $variables);
                    $destination = $this->evaluator->processPlaceholders($metadata['destination'], $variables);

                    if (!$folder || !$destination) {
                        return;
                    }

                    $files = $this->fileRepository->setServer($server)->getDirectory($folder);
                    if (count($files) === 0) {
                        return;
                    }

                    $this->fileRepository->setServer($server)->deleteFiles($destination, collect($files)->map(fn ($file) => $file['name'])->toArray());
                    $this->fileRepository->setServer($server)->renameFiles($destination, collect($files)->map(function ($file) use ($destination, $folder) {
                        return [
                            'from' => $this->getRelativePath($folder . '/' . $file['name'], $destination),
                            'to' => $file['name'],
                        ];
                    })->toArray());

                    break;

                case 'delete':
                    $file = $this->evaluator->processPlaceholders($metadata['file'], $variables);

                    if (!$file) {
                        return;
                    }

                    $this->fileRepository->setServer($server)->deleteFiles(dirname($file), [basename($file)]);

                    break;

                case 'replace':
                case 'append':
                case 'prepend':
                    $file = $this->evaluator->processPlaceholders($metadata['file'], $variables);
                    $fileContent = $this->fileRepository->setServer($server)->getContent($file);

                    if (!$file) {
                        return;
                    }

                    if ($step->action === 'replace') {
                        $search = $this->n($this->evaluator->processPlaceholders($metadata['search'], $variables));

                        $fileContent = str_replace($search, $content, $fileContent);
                    } elseif ($step->action === 'append') {
                        $fileContent .= $content;
                    } elseif ($step->action === 'prepend') {
                        $fileContent = $content . $fileContent;
                    }

                    $this->fileRepository->setServer($server)->putContent($file, $fileContent);

                    break;

                case 'power-action':
                    $state = $this->evaluator->processPlaceholders($metadata['state'], $variables);

                    if ($state === 'start' || $state === 'stop' || $state === 'restart' || $state === 'kill') {
                        $this->powerRepository->setServer($server)->send($state);
                    }

                    break;
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage(), [
                'where' => 'templates.step-handling',
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'step_id' => $step->id,
                'template_id' => $templateId,
                'variable_map' => $variables,
            ]);
        }
    }

    private function getBaseVariableMap(Server $server) {
        $server->loadMissing(['node', 'user', 'allocation', 'allocations', 'egg', 'egg.nest']);

        return [
            'server' => [
                'id' => $server->id,
                'uuid' => $server->uuid,
                'name' => $server->name,
                'node' => [
                    'id' => $server->node->id,
                    'name' => $server->node->name,
                ],
                'user' => [
                    'id' => $server->user->id,
                    'username' => $server->user->username,
                    'email' => $server->user->email,
                ],
                'allocations' => [
                    [
                        'id' => $server->allocation->id,
                        'ip' => $server->allocation->ip,
                        'alias' => $server->allocation->alias,
                        'port' => $server->allocation->port,
                    ],
                    ...$server->allocations
                        ->filter(fn ($allocation) => $allocation->id !== $server->allocation->id)
                        ->map(fn ($allocation) => [
                            'id' => $allocation->id,
                            'ip' => $allocation->ip,
                            'alias' => $allocation->alias,
                            'port' => $allocation->port,
                        ])
                        ->toArray(),
                ],
            ],
            'egg' => [
                'id' => $server->egg->id,
                'name' => $server->egg->name,
                'startup' => $server->egg->startup,
            ],
            'nest' => [
                'id' => $server->egg->nest->id,
                'name' => $server->egg->nest->name,
            ],
        ];
    }

    public function index(TemplateInstallerIndexRequest $request, Server $server): JsonResponse
    {
        $templates = Template::query()->whereJsonContains('eggs', [(string) $server->egg_id])->orderBy('order', 'asc')->orderBy('id', 'asc')->get();

        return new JsonResponse([
            'templates' => $this->fractal->collection($templates)
                ->transformWith($this->getTransformer(TemplateTransformer::class))
                ->toArray(),
        ]);
    }

    public function infos(TemplateInstallerIndexRequest $request, Server $server, string $template): JsonResponse
    {
        /** @var Template|null $template */
        $template = Template::query()->where('id', (int) $template)->whereJsonContains('eggs', [(string) $server->egg_id])->first();
        if (!$template) {
            return new JsonResponse(['error' => 'Template not found'], 404);
        }

        $variableMap = $this->getBaseVariableMap($server);

        return new JsonResponse([
            'variables' => $this->fractal->collection($template->variables()->get())
                ->transformWith($this->getTransformer(TemplateVariableTransformer::class)->setEvaluator($this->evaluator)->setVariableMap($variableMap))
                ->toArray(),
        ]);
    }

    public function install(TemplateInstallerInstallRequest $request, Server $server): JsonResponse
    {
        /** @var Template|null $template */
        $template = Template::query()->where('id', $request->input('template'))->whereJsonContains('eggs', [(string) $server->egg_id])->first();
        if (!$template) {
            return new JsonResponse(['error' => 'Template not found'], 404);
        }

        $variableMap = $this->getBaseVariableMap($server);
        $variableMapWithoutPredefined = [];

        /** @var TemplateVariable $variable */
        foreach ($template->variables()->get() as $variable) {
            $rules = $this->evaluator->processPlaceholders($variable->rules, $variableMap);

            if ($variable->selectable) {
                $index = array_search($variable->id, array_column($request->input('variables'), 'id'));

                $data = $this->validate($request, ["variables.$index.value" => $rules], [], [
                    "variables.$index.value" => $variable->name,
                ]);

                $value = $data['variables'][$index]['value'];
            } else {
                $value = $variable->default_value;
            }

            $variableMap[$variable->variable] = $value;
            $variableMapWithoutPredefined[$variable->variable] = $value;
        }

        if ($template->startup_command) {
            $server->update(['startup' => $this->evaluator->processPlaceholders($template->startup_command, $variableMap)]);
        }

        if ($template->force_wipe) {
            $this->powerRepository->setServer($server)->send('kill');

            $files = $this->fileRepository->setServer($server)->getDirectory('/');

            if (count($files) > 0) {
                $this->fileRepository->setServer($server)->deleteFiles(
                    '/',
                    collect($files)->map(fn ($file) => $file['name'])->toArray()
                );
            }
        }

        foreach ($template->steps()->orderBy('order', 'asc')->orderBy('id', 'asc')->get() as $step) {
            $this->handleStep($step, $variableMap, $server, $template->id);
        }

        if ($template->force_reinstall) {
            $this->reinstallService->handle($server);
        }

        Activity::event('server:template.install')
            ->property('template', $template->name)
            ->property('variables', $variableMapWithoutPredefined)
            ->log();

        return new JsonResponse([], JsonResponse::HTTP_NO_CONTENT);
    }
}
