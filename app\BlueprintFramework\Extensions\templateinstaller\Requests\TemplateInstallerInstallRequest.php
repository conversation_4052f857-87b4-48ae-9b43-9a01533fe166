<?php

namespace Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Requests;

use Pterodactyl\Http\Requests\Api\Client\ClientApiRequest;

class TemplateInstallerInstallRequest extends ClientApiRequest
{
    public function permission(): string
    {
        return 'templates.install';
    }

    public function rules(): array
    {
        return [
            'template' => 'required|int|exists:templates,id',
            'variables' => 'present|array|min:0',
            'variables.*.id' => 'required|integer|exists:template_variables,id',
            'variables.*.value' => 'nullable|string',
        ];
    }
}
