<?php

namespace Pterodactyl\Http\Controllers\Api\Client\Servers\PocketAddons;

use Carbon\CarbonImmutable;
use CURLFile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Storage;
use Pterodactyl\Http\Controllers\Controller;
use Pterodactyl\Models\Server;
use Pterodactyl\Services\Nodes\NodeJWTService;

class InstallAddon extends Controller
{

    protected $category_folders = [
        'Plugins' => '/plugins',
    ];

    /**
     * Constructor
     */
    public function __construct(
        private NodeJWTService $jwtService
    )
    {}

    /**
     * Install an addon
     */
    public function install(Request $request, Server $server)
    {

        RateLimiter::for('pocketaddons.i', function () {
            return Request::ip();
        });

        RateLimiter::hit('pocketaddons.i');

        if (RateLimiter::tooManyAttempts('pocketaddons.i', $perMinute = 5)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Too many requests'
            ], 429);
        }

        $addon_id = $request->input('ressource_id');
        
        // get https://pocketaddons.com/api/ressources/ and find the addon with the id $addon_id
        // then get the version_file link and download it
        // upload it to the server

        $api_response = Http::get('https://pocketaddons.com/api/ressources/');
        $addons = $api_response->json();

        $addon = null;
        foreach ($addons as $addon) {
            if ($addon['ressource_id'] == $addon_id) {
                $addon = $addon;
                break;
            }
        }

        if ($addon == null) {
            return response()->json([
                'status' => 'error',
                'message' => 'Addon not found'
            ]);
        }

        $_version_file = substr($addon['version_file'], 1);

        $version_file = file_get_contents("https://pocketaddons.com/api/versions/download/?id=".$addon['version_id']);
        // this return the .phar file

        // get extension of the file
        $extension = pathinfo($_version_file, PATHINFO_EXTENSION);

        // save the file
        Storage::disk('local')->put('plugins/'.$addon['ressource_name'].'.'.$extension, $version_file);

        // upload it to wings 

        $token = $this->jwtService
            ->setExpiresAt(CarbonImmutable::now()->addMinutes(15))
            ->setUser($request->user())
            ->setClaims(['server_uuid' => $server->uuid])
            ->handle($server->node, $request->user()->id . $server->uuid);

        $upload_url = sprintf(
            '%s/upload/file?token=%s&directory=%s',
            $server->node->getConnectionAddress(),
            $token->toString(),
            urlencode($this->category_folders[$addon['category_name']])
        );

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $upload_url,
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => array(
                'files' => new CURLFile(storage_path('app/plugins/'.$addon['ressource_name'].'.'.$extension))
            ),
            CURLOPT_HTTPHEADER => array(
                "Accept: application/json, text/plain, */*"
            ),
            CURLOPT_RETURNTRANSFER => true, // Return the transfer as a string
        ));

        $response = curl_exec($curl);
        $error = curl_error($curl);
        $errorCode = curl_errno($curl);

        if ($errorCode) {
            // If there's a cURL error, handle it
            return response()->json([
                'status' => 'error',
                'message' => 'cURL error: ' . $error
            ], 500);
        }

        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        if ($httpCode >= 400) {
            // If the HTTP status code indicates an error, handle it
            return response()->json([
                'status' => 'error',
                'message' => 'HTTP error: ' . $httpCode
            ], $httpCode);
        }

        curl_close($curl);

        Storage::disk('local')->delete('plugins/'.$addon['ressource_name'].'.phar');

        // If everything went well, continue with your response
        return response()->json([
            'status' => 'success',
            'message' => 'Addon installed'
        ]);

    }
}