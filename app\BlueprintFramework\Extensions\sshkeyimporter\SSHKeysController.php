<?php

namespace Pterodactyl\BlueprintFramework\Extensions\sshkeyimporter;

use Illuminate\Support\Facades\Http;
use Pterodactyl\Exceptions\DisplayException;
use Pterodactyl\Http\Controllers\Api\Client\ClientApiController;

class SSHKeysController extends ClientApiController
{
    public function getKeys(SSHKeyGetKeysRequest $request): array
    {
        $provider = $request->route('provider');
        $username = $request->input('username');
        $result = [];

        try {
            switch ($provider) {
                case 'github': {
                    $keys = Http::get("https://api.github.com/users/{$username}/keys")->json();

                    for ($i = 0; $i < count($keys); $i++) {
                        $result[] = $keys[$i]['key'];
                    }

                    break;
                }

                case 'gitlab': {
                    $keys = Http::get("https://gitlab.com/api/v4/users/{$username}/keys")->json();

                    for ($i = 0; $i < count($keys); $i++) {
                        $result[] = $keys[$i]['key'];
                    }

                    break;
                }

                case 'launchpad': {
                    $keys = Http::get("https://api.launchpad.net/1.0/~{$username}/sshkeys")->json()['entries'];

                    for ($i = 0; $i < count($keys); $i++) {
                        $result[] = $keys[$i]['keytext'];
                    }

                    break;
                }
            }
        } catch (\Exception $exception) {
            throw new DisplayException('There was an error while attempting to fetch SSH keys from the provider.');
        }

        return [
            'data' => $result,
        ];
    }
}
