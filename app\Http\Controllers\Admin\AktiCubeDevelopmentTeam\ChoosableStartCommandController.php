<?php

namespace Pterodactyl\Http\Controllers\Admin\AktiCubeDevelopmentTeam;

use Prologue\Alerts\AlertsMessageBag;
use Pterodactyl\Http\Controllers\Controller;
use Pterodactyl\Http\Requests\Admin\AktiCubeDevelopmentTeam\StoreChoosableStartCommandRequest;
use Pterodactyl\Http\Requests\Admin\AktiCubeDevelopmentTeam\UpdateChoosableStartCommandRequest;
use Pterodactyl\Models\StartCommand;
use Pterodactyl\Repositories\Eloquent\NestRepository;

class ChoosableStartCommandController extends Controller
{
    protected $alert;

    protected $nestRepository;

    public function __construct(AlertsMessageBag $alert, NestRepository $nestRepository)
    {
        $this->alert = $alert;
        $this->nestRepository = $nestRepository;
    }

    public function index(): \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Contracts\Foundation\Application
    {
        $start_commands = StartCommand::all();
        return view('admin.akticube.choosable_start_command.index', [
            'start_commands' => $start_commands,
        ]);
    }

    public function create(): \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Contracts\Foundation\Application
    {
        $nests = $this->nestRepository->getWithEggs();
        return view('admin.akticube.choosable_start_command.new', [
            'nests' => $nests,
        ]);
    }

    public function store(StoreChoosableStartCommandRequest $request): \Illuminate\Http\RedirectResponse
    {
        StartCommand::query()->create($request->only([
            'name',
            'description',
            'start_command',
            'linked_egg_id',
        ]));

        $this->alert->success('This Start Command was successfully created.')->flash();
        return redirect()->route('admin.akticube.choosable-start-command');
    }

    public function destroy(int $choosablestartcommand_id): \Illuminate\Http\RedirectResponse
    {
        $start_command = StartCommand::query()->findOrFail($choosablestartcommand_id);
        $start_command->delete();

        $this->alert->success('This Start Command was successfully deleted.')->flash();
        return redirect()->route('admin.akticube.choosable-start-command');
    }

    public function view(int $choosablestartcommand_id): \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Contracts\Foundation\Application
    {
        $start_command = StartCommand::query()->findOrFail($choosablestartcommand_id);

        $nests = $this->nestRepository->getWithEggs();
        return view('admin.akticube.choosable_start_command.view', [
            'start_command' => $start_command,
            'nests' => $nests,
        ]);
    }

    public function update(UpdateChoosableStartCommandRequest $request, int $choosablestartcommand_id): \Illuminate\Http\RedirectResponse
    {
        $start_command = StartCommand::query()->findOrFail($choosablestartcommand_id);
        $start_command->update($request->only([
            'name',
            'description',
            'start_command',
            'linked_egg_id',
        ]));

        $this->alert->success('This Start Command was successfully updated.')->flash();
        return redirect()->route('admin.akticube.choosable-start-command');
    }
}
