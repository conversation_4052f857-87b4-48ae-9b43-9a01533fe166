<?php

namespace Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models;

use Pterodactyl\Models\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * TemplateVariable.
 *
 * @property int $id
 * @property int $order
 * @property int $template_id
 * @property string $name
 * @property string $variable
 * @property string|null $description
 * @property string|null $default_value
 * @property string $rules
 * @property bool $selectable
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models\Template $template
 *
 * @method static \Database\Factories\TemplateVariable factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|TemplateVariable newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TemplateVariable newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TemplateVariable query()
 * @method static \Illuminate\Database\Eloquent\Builder|TemplateVariable whereId($value)
 *
 * @mixin \Eloquent
 */
class TemplateVariable extends Model
{
    /**
     * The resource name for this model when it is transformed into an
     * API representation using fractal.
     */
    public const RESOURCE_NAME = 'template_variable';

    /**
     * The table associated with the model.
     */
    protected $table = 'template_variables';

    /**
     * Cast values to correct type.
     */
    protected $casts = [
        'selectable' => 'bool',
        self::CREATED_AT => 'datetime',
        self::UPDATED_AT => 'datetime',
    ];

    /**
     * Fields that are mass assignable.
     */
    protected $fillable = [
        'order',
        'template_id',
        'name',
        'variable',
        'description',
        'default_value',
        'rules',
    ];

    /**
     * Rules to protect against invalid data entry to DB.
     */
    public static array $validationRules = [
        'order' => 'required|integer',
        'template_id' => 'required|exists:templates,id',
        'name' => 'required|string',
        'variable' => 'required|string',
        'description' => 'nullable|string',
        'default_value' => 'nullable|string',
        'rules' => 'required|string',
    ];

    /**
     * Returns the template this variable is assigned to.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class, 'template_id');
    }
}
