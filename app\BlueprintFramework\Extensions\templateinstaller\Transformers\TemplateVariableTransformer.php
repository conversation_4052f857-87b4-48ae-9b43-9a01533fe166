<?php

namespace Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Transformers;

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use Pterodactyl\Transformers\Api\Client\BaseClientTransformer;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models\TemplateVariable;
use Pterodactyl\BlueprintFramework\Extensions\templateinstaller\SecureExpressionEvaluator;

function getRuleDescription(string|array $rules): array 
{
    $rules = is_string($rules) ? ['field' => $rules] : $rules;
    
    $allMessages = [];
    
    foreach ($rules as $field => $fieldRules) {
        if (is_string($fieldRules)) {
            $fieldRules = explode('|', $fieldRules);
        }
        
        foreach ($fieldRules as $rule) {
            $ruleName = is_string($rule) ? explode(':', $rule)[0] : (
                is_object($rule) ? Str::snake(class_basename($rule)) : $rule
            );

            $testValue = match($ruleName) {
                'required' => '',
                'string' => 123,
                'min' => 'a',
                'max' => str_repeat('a', 999999),
                'email' => 'not-an-email',
                'numeric' => 'not-a-number',
                'integer' => 'not-an-integer',
                'array' => 'not-an-array',
                'date' => 'not-a-date',
                'boolean' => 'not-a-boolean',
                'json' => 'not-json',
                'url' => 'not-a-url',
                'ip' => 'not-an-ip',
                'alpha' => '123',
                'alpha_num' => '!!!',
                'alpha_dash' => '!!!',
                'regex' => '<',
                'date_format' => 'not-a-date',
                'timezone' => 'not-a-timezone',

                default => null
            };
            
            $validator = Validator::make(
                [$field => $testValue], 
                [$field => $rule]
            );
            
            $validator->fails();
            $messages = $validator->errors()->all();
            
            if (!empty($messages)) {
                $allMessages[] = preg_replace(
                    ['/^The\s+field\s+/i', '/^The\s+field\s+/i'],
                    '',
                    $messages[0]
                );
            }
        }
    }
    
    return array_map(
        fn($msg) => ucfirst(trim($msg)),
        array_unique($allMessages)
    );
}

class TemplateVariableTransformer extends BaseClientTransformer
{
    private $variableMap = [];
    private SecureExpressionEvaluator $evaluator;

    public function getResourceName(): string
    {
        return 'template_variable';
    }

    public function setVariableMap(array $map): self
    {
        $this->variableMap = $map;

        return $this;
    }

    public function setEvaluator(SecureExpressionEvaluator $evaluator): self
    {
        $this->evaluator = $evaluator;

        return $this;
    }

    public function transform(TemplateVariable $variable): array
    {
        $rules = $this->evaluator->processPlaceholders($variable->rules, $this->variableMap);

        return [
            'id' => $variable->id,
            'name' => $variable->name,
            'description' => $variable->description,
            'default_value' => $variable->default_value,
            'rules' => explode('|', $rules),
            'rules_readable' => getRuleDescription($rules),
            'selectable' => $variable->selectable,
        ];
    }
}
