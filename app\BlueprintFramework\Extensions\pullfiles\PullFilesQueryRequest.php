<?php

namespace Pterodactyl\BlueprintFramework\Extensions\pullfiles;

use Pterodactyl\Models\Permission;
use Pterodactyl\Http\Requests\Api\Client\ClientApiRequest;

class PullFilesQueryRequest extends ClientApiRequest
{
    public function permission(): string
    {
        return Permission::ACTION_FILE_UPDATE;
    }

    /**
     * Rules to validate this request against.
     */
    public function rules(): array
    {
        return [
            'url' => 'required|url',
        ];
    }
}
