<?php

/**
 * BlueprintExtensionLibrary (<PERSON>sole variation)
 *
 * @category   BlueprintExtensionLibrary
 * @package    BlueprintConsoleLibrary
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023-2024 Emma (prpl.wtf)
 * @license    https://blueprint.zip/docs/?page=about/License MIT License
 * @link       https://blueprint.zip/docs/?page=documentation/$blueprint
 * @since      beta
 */

namespace Pterodactyl\BlueprintFramework\Libraries\ExtensionLibrary\Console;

use Pterodactyl\BlueprintFramework\Libraries\ExtensionLibrary\BlueprintBaseLibrary;

class BlueprintConsoleLibrary extends BlueprintBaseLibrary
{
}
