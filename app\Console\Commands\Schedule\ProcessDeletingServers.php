<?php

namespace Pterodactyl\Console\Commands\Schedule;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Database\ConnectionInterface;
use Illuminate\Support\Facades\Log;
use Pterodactyl\Extensions\Backups\BackupManager;
use Pterodactyl\Models\Backup;
use Pterodactyl\Models\BackupToKeep;
use Pterodactyl\Models\DeletingServers;
use Pterodactyl\Models\Server;
use Pterodactyl\Repositories\Wings\DaemonBackupRepository;
use Pterodactyl\Repositories\Wings\DaemonServerRepository;
use Pterodactyl\Services\Backups\DeleteBackupService;
use Pterodactyl\Services\Databases\DatabaseManagementService;

class ProcessDeletingServers extends Command
{
    protected $signature = 'p:schedule:deleteservers';

    protected $description = 'Delete queued servers in the database and determine which are ready to delete.';

    public function __construct(
        private ConnectionInterface $connection,
        private DaemonServerRepository $daemonServerRepository,
        private BackupManager $manager,
        private DatabaseManagementService $databaseManagementService,
        private DaemonBackupRepository $daemonBackupRepository,
        private DeleteBackupService $deleteBackupService,
    ) {
        parent::__construct();
    }


    /**
     * Handle command execution.
     */
    public function handle(): int
    {
        $servers = DeletingServers::query()->get();

        if ($servers->count() < 1) {
            $this->line('There are no servers that need to be deleted.');
        }

        foreach ($servers as $server) {
            $this->line('Deleting server ' . $server->server_uuid . '...');
            try {
                $server = Server::whereUuid($server->server_uuid)->firstOrFail();
            } catch (Exception $exception) {
                DeletingServers::query()->where('server_uuid', $server->server_uuid)->delete();
                continue;
            }

            //verify last backup is completed
            if ($server->backup_limit > 0) {
                $lastBackup = $server->backups()->orderByDesc('created_at')->first();
                if ($lastBackup && $lastBackup->is_successful == 0) {
                    $this->line('Last backup is not completed, skipping...');
                    continue;
                } else if ($lastBackup) {
			
		    $size = $lastBackup->bytes / 1024 / 1024;
                    $size = round($size, 2);
                    $this->line('Last backup size: ' . $size . 'MiB');
                    BackupToKeep::query()->where('server_uuid', $server->uuid)->update(['size' => $size . "MiB"]);
		}
		else {
		    $this->daemonServerRepository->setServer($server)->delete();
		    continue ;
		}
            }

            // remove backups
            $this->line('Removing backups...');
            $backups = Backup::query()->where('server_id', $server->id)->get();
            foreach ($backups as $backup) {
                $this->deleteBackupService->handle($backup);
            }

            try {
                $this->daemonServerRepository->setServer($server)->delete();
            } catch (DaemonConnectionException $exception) {
                // If there is an error not caused a 404 error and this isn't a forced delete,
                // go ahead and bail out. We specifically ignore a 404 since that can be assumed
                // to be a safe error, meaning the server doesn't exist at all on Wings so there
                // is no reason we need to bail out from that.
                if ($exception->getStatusCode() !== Response::HTTP_NOT_FOUND) {
                    throw $exception;
                }

                Log::warning($exception);
            }

            $this->connection->transaction(function () use ($server) {
                foreach ($server->databases as $database) {
                    try {
                        $this->databaseManagementService->delete($database);
                    } catch (Exception $exception) {
                        if (!true) {
                            throw $exception;
                        }

                        // Oh well, just try to delete the database entry we have from the database
                        // so that the server itself can be deleted. This will leave it dangling on
                        // the host instance, but we couldn't delete it anyways so not sure how we would
                        // handle this better anyways.
                        //
                        // @see https://github.com/pterodactyl/panel/issues/2085
                        $database->delete();

                        Log::warning($exception);
                    }
                }

                $server->delete();
                DeletingServers::query()->where('server_uuid', $server->uuid)->delete();
            });
        }

        $backups = BackupToKeep::query()->where('delete_at', '<=', now())->get();

        $this->line('There are ' . $backups->count() . ' backups that need to be deleted.');

        if ($backups->count() > 0) {
            foreach ($backups as $backup) {
                $this->line($backup->delete_at . ' - ' . $backup->server_uuid . ' - ' . $backup->backup_uuid);
                $this->line('Deleting backup ' . $backup->backup_uuid . '...');

                if ($backup->disk === Backup::ADAPTER_AWS_S3) {
                    $this->connection->transaction(function () use ($backup) {
                        $backup->delete();
                        $this->line('Backup deleted from database. (S3)');
                        /** @var \Pterodactyl\Extensions\Filesystem\S3Filesystem $adapter */
                        $adapter = $this->manager->adapter(Backup::ADAPTER_AWS_S3);

                        $adapter->getClient()->deleteObject([
                            'Bucket' => $adapter->getBucket(),
                            'Key' => sprintf('%s/%s.tar.gz', $backup->server_uuid, $backup->backup_uuid),
                        ]);
                    });
                } else {
                    $this->connection->transaction(function () use ($backup) {
                        try {
                            $this->daemonBackupRepository->setServer($backup->server)->delete($backup);
                        } catch (DaemonConnectionException $exception) {
                            $previous = $exception->getPrevious();
                            // Don't fail the request if the Daemon responds with a 404, just assume the backup
                            // doesn't actually exist and remove its reference from the Panel as well.
                            if (!$previous instanceof ClientException || $previous->getResponse()->getStatusCode() !== Response::HTTP_NOT_FOUND) {
                                throw $exception;
                            }
                        }
                        $this->line('Backup deleted from daemon. (Local)');
                        $backup->delete();
                    });
                }
            }
        }

        $this->line('');

        return 0;
    }
}
