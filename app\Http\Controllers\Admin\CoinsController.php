<?php

namespace Pterodactyl\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Pterodactyl\Models\User;
use Pterodactyl\Models\UserAdWatch;
use Pterodactyl\Http\Controllers\Controller;
use Prologue\Alerts\AlertsMessageBag;

class CoinsController extends Controller
{
    /**
     * @var \Prologue\Alerts\AlertsMessageBag
     */
    protected $alert;

    /**
     * CoinsController constructor.
     *
     * @param \Prologue\Alerts\AlertsMessageBag $alert
     */
    public function __construct(AlertsMessageBag $alert)
    {
        $this->alert = $alert;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request): View
    {
        // Lấy danh sách người dùng với số xu theo thứ tự giảm dần
        $users = User::orderBy('coins', 'desc')
            ->paginate(25);

        // Thống kê tổng số xu trong hệ thống
        $totalCoins = User::sum('coins');
        
        // Thống kê số xu phát hành thông qua ads
        $totalCoinsFromAds = UserAdWatch::sum('coins_earned');

        return view('admin.coins.index', [
            'users' => $users,
            'totalCoins' => $totalCoins,
            'totalCoinsFromAds' => $totalCoinsFromAds,
        ]);
    }

    /**
     * Display coin history for a specific user.
     *
     * @param \Pterodactyl\Models\User $user
     * @return \Illuminate\View\View
     */
    public function viewUserHistory(User $user): View
    {
        $history = UserAdWatch::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(50);

        return view('admin.coins.history', [
            'user' => $user,
            'history' => $history,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param \Pterodactyl\Models\User $user
     * @return \Illuminate\View\View
     */
    public function edit(User $user): View
    {
        return view('admin.coins.edit', [
            'user' => $user,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Pterodactyl\Models\User $user
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $validated = $request->validate([
            'coins' => 'required|integer|min:0',
            'action' => 'required|string|in:set,add,subtract',
        ]);

        $oldCoins = $user->coins;
        $newCoins = $oldCoins;

        switch ($validated['action']) {
            case 'set':
                $newCoins = $validated['coins'];
                break;
            case 'add':
                $newCoins = $oldCoins + $validated['coins'];
                break;
            case 'subtract':
                $newCoins = max(0, $oldCoins - $validated['coins']);
                break;
        }

        $user->update([
            'coins' => $newCoins,
        ]);

        $this->alert->success("Coins for user {$user->username} updated successfully. Old balance: {$oldCoins}, New balance: {$newCoins}.")->flash();

        return redirect()->route('admin.coins.index');
    }

    /**
     * Display statistics about coin usage in the system.
     *
     * @return \Illuminate\View\View
     */
    public function statistics(): View
    {
        // Thống kê tổng số xu trong hệ thống
        $totalCoins = User::sum('coins');
        
        // Thống kê số xu phát hành thông qua ads
        $totalCoinsFromAds = UserAdWatch::sum('coins_earned');
        
        // Top 10 người dùng có nhiều xu nhất
        $topUsers = User::orderBy('coins', 'desc')
            ->take(10)
            ->get();
            
        // Tổng số người dùng có xu > 0
        $usersWithCoins = User::where('coins', '>', 0)->count();
        
        return view('admin.coins.statistics', [
            'totalCoins' => $totalCoins,
            'totalCoinsFromAds' => $totalCoinsFromAds,
            'topUsers' => $topUsers,
            'usersWithCoins' => $usersWithCoins
        ]);
    }

    /**
     * Add coins to all users.
     *
     * @return \Illuminate\View\View
     */
    public function massAddForm(): View
    {
        return view('admin.coins.mass-add');
    }
    
    /**
     * Process mass addition of coins to users.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function massAdd(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'coins' => 'required|integer|min:1',
            'condition' => 'required|string|in:all,active,inactive',
        ]);
        
        $query = User::query();
        
        if ($validated['condition'] === 'active') {
            $query->where('root_admin', true);
        } else if ($validated['condition'] === 'inactive') {
            $query->where('root_admin', false);
        }
        
        $count = $query->count();
        
        // Thêm xu cho người dùng dựa trên điều kiện
        $query->increment('coins', $validated['coins']);
        
        $this->alert->success("Đã thêm {$validated['coins']} xu cho {$count} người dùng thành công.")->flash();
        return redirect()->route('admin.coins.index');
    }
    
    /**
     * Show history of all coin transactions.
     *
     * @return \Illuminate\View\View
     */
    public function allHistory(): View
    {
        $history = UserAdWatch::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(50);
            
        return view('admin.coins.all-history', [
            'history' => $history
        ]);
    }
    
    /**
     * Set coin value for multiple users.
     *
     * @return \Illuminate\View\View
     */
    public function batchEditForm(): View
    {
        return view('admin.coins.batch-edit');
    }
    
    /**
     * Process batch editing of user coins.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function batchEdit(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'user_ids' => 'required|string',
            'coins' => 'required|integer|min:0',
            'action' => 'required|string|in:set,add,subtract',
        ]);
        
        $userIds = explode(',', $validated['user_ids']);
        $users = User::whereIn('id', $userIds)->get();
        
        foreach ($users as $user) {
            if ($validated['action'] === 'set') {
                $user->coins = $validated['coins'];
            } else if ($validated['action'] === 'add') {
                $user->coins += $validated['coins'];
            } else if ($validated['action'] === 'subtract') {
                $user->coins = max(0, $user->coins - $validated['coins']);
            }
            $user->save();
        }
        
        $this->alert->success('Đã cập nhật xu cho ' . count($users) . ' người dùng thành công.')->flash();
        return redirect()->route('admin.coins.index');
    }

    /**
     * Show the settings page for coin system.
     *
     * @return \Illuminate\View\View
     */
    public function showSettings(): View
    {
        return view('admin.coins.settings');
    }

    /**
     * Update coin system settings.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSettings(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'coin_system_enabled' => 'required|in:0,1',
            'coin_reward' => 'required|integer|min:1',
            'ad_cooldown' => 'required|integer|min:1',
            'required_view_time' => 'required|integer|min:5',
            'max_view_time' => 'required|integer|min:30',
            'adsense_publisher_id' => 'required|string',
            'adsense_ad_slot' => 'required|string',
        ]);

        $enabledValue = ($validated['coin_system_enabled'] === '1') ? 'true' : 'false';
        
        // Debug values before update
        \Log::debug('Before update - Coin system status:', [
            'input' => $validated['coin_system_enabled'],
            'env_value' => env('PTERODACTYL_COINS_ENABLED'),
            'config_value' => config('pterodactyl.coins.enabled'),
        ]);
        
        // DIRECT UPDATE: Update the environment variable in .env file
        $envPath = base_path('.env');
        if (file_exists($envPath)) {
            $envContent = file_get_contents($envPath);
            
            // Replace or add the environment variable
            if (strpos($envContent, 'PTERODACTYL_COINS_ENABLED=') !== false) {
                $envContent = preg_replace('/^PTERODACTYL_COINS_ENABLED=.*$/m', "PTERODACTYL_COINS_ENABLED={$enabledValue}", $envContent);
            } else {
                $envContent .= "\nPTERODACTYL_COINS_ENABLED={$enabledValue}";
            }
            
            file_put_contents($envPath, $envContent);
        }

        // Update the rest of the environment variables
        $this->updateEnvironmentVariable('PTERODACTYL_COINS_AD_REWARD', $validated['coin_reward']);
        $this->updateEnvironmentVariable('PTERODACTYL_COINS_AD_COOLDOWN', $validated['ad_cooldown']);
        $this->updateEnvironmentVariable('PTERODACTYL_COINS_REQUIRED_VIEW_TIME', $validated['required_view_time']);
        $this->updateEnvironmentVariable('PTERODACTYL_COINS_MAX_VIEW_TIME', $validated['max_view_time']);
        $this->updateEnvironmentVariable('PTERODACTYL_COINS_ADSENSE_PUBLISHER_ID', $validated['adsense_publisher_id']);
        $this->updateEnvironmentVariable('PTERODACTYL_COINS_ADSENSE_AD_SLOT', $validated['adsense_ad_slot']);

        // Force update the runtime configuration
        $enabled = $enabledValue === 'true';
        
        // Set directly in config
        config(['pterodactyl.coins.enabled' => $enabled]);
        
        // Clear caches to ensure changes take effect
        \Artisan::call('config:clear');
        \Artisan::call('cache:clear');
        
        // Clear OPCache if available
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
        
        // Debug values after update
        \Log::debug('After update - Coin system status:', [
            'updated_to' => $enabledValue,
            'env_value' => env('PTERODACTYL_COINS_ENABLED'),
            'config_value' => config('pterodactyl.coins.enabled'),
            'is_enabled' => $enabled,
        ]);

        $this->alert->success('Coin system settings have been updated successfully. You may need to restart your queue workers for some changes to take effect.')->flash();

        return redirect()->route('admin.coins.settings');
    }

    /**
     * Update environment variable in the .env file.
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    protected function updateEnvironmentVariable($key, $value)
    {
        $path = base_path('.env');

        if (file_exists($path)) {
            // Read the current .env file content
            $content = file_get_contents($path);

            // Special handling for boolean values
            if ($key === 'PTERODACTYL_COINS_ENABLED') {
                $value = $value === '1' ? 'true' : 'false';
                \Log::debug('Updating PTERODACTYL_COINS_ENABLED:', [
                    'input_value' => $value,
                    'raw_value' => env('PTERODACTYL_COINS_ENABLED'),
                    'config_value' => config('pterodactyl.coins.enabled'),
                ]);
            }

            // If the key exists, replace its value
            if (strpos($content, "{$key}=") !== false) {
                $content = preg_replace("/^{$key}=.*/m", "{$key}={$value}", $content);
            } else {
                // If the key doesn't exist, add it
                $content .= "\n{$key}={$value}";
            }

            // Write the updated content back to the .env file
            file_put_contents($path, $content);
            
            // Directly update the $_ENV and $_SERVER variables
            $_ENV[$key] = $value;
            $_SERVER[$key] = $value;
            
            // Clear any cached values
            if (function_exists('opcache_reset')) {
                opcache_reset();
            }
            
            // Update the application config directly
            if ($key === 'PTERODACTYL_COINS_ENABLED') {
                $boolValue = $value === 'true';
                config(['pterodactyl.coins.enabled' => $boolValue]);
            }
        }
    }

    /**
     * Reload the configuration to ensure changes take effect.
     *
     * @return void
     */
    protected function reloadConfig()
    {
        // Clear all caches
        \Artisan::call('config:clear');
        \Artisan::call('cache:clear');
        
        // Force reload by updating directly
        $envValue = env('PTERODACTYL_COINS_ENABLED', 'false');
        $boolValue = $envValue === 'true';
        
        // Update the config directly
        config(['pterodactyl.coins.enabled' => $boolValue]);
        
        \Log::debug('Reloaded config:', [
            'env_value' => $envValue,
            'parsed_as' => $boolValue,
            'config_value' => config('pterodactyl.coins.enabled'),
        ]);
    }
    
    /**
     * Show nodes with coin system status.
     *
     * @return \Illuminate\View\View
     */
    public function showNodesStatus(): View
    {
        $nodes = \Pterodactyl\Models\Node::withCount('servers')
            ->orderBy('name')
            ->get();
            
        return view('admin.coins.nodes', [
            'nodes' => $nodes,
        ]);
    }
    
    /**
     * Toggle coin system for a specific node.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Pterodactyl\Models\Node $node
     * @return \Illuminate\Http\RedirectResponse
     */
    public function toggleNodeCoins(Request $request, \Pterodactyl\Models\Node $node): RedirectResponse
    {
        $node->update([
            'coins_enabled' => !$node->coins_enabled,
        ]);
        
        $this->alert->success('Đã ' . ($node->coins_enabled ? 'bật' : 'tắt') . ' hệ thống xu cho node ' . $node->name . ' thành công.')->flash();
        
        return redirect()->route('admin.coins.nodes');
    }
} 