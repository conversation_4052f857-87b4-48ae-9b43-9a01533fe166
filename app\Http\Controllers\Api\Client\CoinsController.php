<?php

namespace Pterodactyl\Http\Controllers\Api\Client;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;
use Pterodactyl\Models\User;
use Pterodactyl\Models\UserAdWatch;
use Pterodactyl\Transformers\Api\Client\CoinsTransformer;
use Pterodactyl\Http\Controllers\Api\Client\ClientApiController;
use Pterodactyl\Models\AdWatchHistory;

class CoinsController extends ClientApiController
{
    /**
     * Get the current balance of coins for the authenticated user.
     *
     * @return array
     */
    public function balance()
    {
        return $this->fractal->item($this->request->user())
            ->transformWith($this->getTransformer(CoinsTransformer::class))
            ->toArray();
    }

    /**
     * Record that a user has watched an ad and award coins.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function watchAd(Request $request)
    {
        /** @var \Pterodactyl\Models\User $user */
        $user = $this->request->user();

        // IMPORTANT: Check if coin system is enabled by directly reading .env file
        $systemEnabled = false;
        $envPath = base_path('.env');
        if (file_exists($envPath)) {
            $envContent = file_get_contents($envPath);
            if (preg_match('/^PTERODACTYL_COINS_ENABLED=(.*)$/m', $envContent, $matches)) {
                $systemEnabled = trim($matches[1]) === 'true';
            }
        }
        
        // Get active server if available
        $serverId = request()->input('server_id');
        $nodeEnabled = true;
        
        if ($serverId) {
            $server = \Pterodactyl\Models\Server::where('id', $serverId)
                ->where('user_id', $user->id)
                ->first();
                
            if ($server) {
                $node = $server->node;
                if ($node) {
                    $nodeEnabled = $node->coins_enabled;
                }
            }
        }
        
        // Final check - both system and node must have coins enabled
        $coinsEnabled = $systemEnabled && $nodeEnabled;
        
        \Log::debug('WatchAd - Coin system enabled:', [
            'config_value' => config('pterodactyl.coins.enabled'),
            'env_value' => env('PTERODACTYL_COINS_ENABLED'),
            'direct_env_read' => $systemEnabled,
            'node_enabled' => $nodeEnabled,
            'final_enabled' => $coinsEnabled,
            'server_id' => $serverId,
        ]);

        if (!$coinsEnabled) {
            return response()->json([
                'error' => $nodeEnabled ? 'Hệ thống xu đang bị tắt.' : 'Hệ thống xu không khả dụng cho máy chủ này.',
            ], 403);
        }

        // Kiểm tra xem người dùng có thể xem quảng cáo không (thời gian giữa các lần xem)
        $lastAdWatch = UserAdWatch::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->first();

        $cooldownHours = config('pterodactyl.coins.ad_cooldown_hours', 6);
        
        if ($lastAdWatch) {
            $nextAvailableTime = $lastAdWatch->created_at->addHours($cooldownHours);
            if ($nextAvailableTime->isFuture()) {
                $timeLeft = now()->diffForHumans($nextAvailableTime, ['syntax' => \Carbon\CarbonInterface::DIFF_ABSOLUTE]);
                return response()->json([
                    'error' => "Bạn phải đợi thêm {$timeLeft} trước khi xem quảng cáo tiếp theo.",
                ], 429);
            }
        }

        // Số xu người dùng nhận được cho mỗi lần xem quảng cáo
        $coinsEarned = config('pterodactyl.coins.ad_reward', 10);

        // Ghi lại lịch sử xem quảng cáo
        $adWatch = new UserAdWatch();
        $adWatch->user_id = $user->id;
        $adWatch->coins_earned = $coinsEarned;
        $adWatch->save();

        // Cập nhật số xu của người dùng
        $user->increment('coins', $coinsEarned);

        // Cập nhật thời gian xem quảng cáo cuối cùng
        $user->update([
            'last_ad_watch' => Carbon::now(),
        ]);

        return response()->json([
            'success' => true,
            'coins_earned' => $coinsEarned,
            'total_coins' => $user->coins,
        ]);
    }

    /**
     * Get user's ad watch history.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function history(Request $request): JsonResponse
    {
        $history = UserAdWatch::where('user_id', $request->user()->id)
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return new JsonResponse([
            'data' => $history->items(),
            'pagination' => [
                'current_page' => $history->currentPage(),
                'total_pages' => $history->lastPage(),
            ],
        ]);
    }

    /**
     * Check if the user can watch an ad to earn coins.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkAdAvailability()
    {
        // IMPORTANT: Check if coin system is enabled by directly reading .env file
        $systemEnabled = false;
        $envPath = base_path('.env');
        if (file_exists($envPath)) {
            $envContent = file_get_contents($envPath);
            if (preg_match('/^PTERODACTYL_COINS_ENABLED=(.*)$/m', $envContent, $matches)) {
                $systemEnabled = trim($matches[1]) === 'true';
            }
        }
        
        // Get active server if available
        $user = $this->request->user();
        $serverId = request()->input('server_id');
        $nodeEnabled = true;
        
        if ($serverId) {
            $server = \Pterodactyl\Models\Server::where('id', $serverId)
                ->where('user_id', $user->id)
                ->first();
                
            if ($server) {
                $node = $server->node;
                if ($node) {
                    $nodeEnabled = $node->coins_enabled;
                }
            }
        }
        
        // Final check - both system and node must have coins enabled
        $coinsEnabled = $systemEnabled && $nodeEnabled;
        
        \Log::debug('CheckAdAvailability - Coin system enabled:', [
            'config_value' => config('pterodactyl.coins.enabled'),
            'env_value' => env('PTERODACTYL_COINS_ENABLED'),
            'direct_env_read' => $systemEnabled,
            'node_enabled' => $nodeEnabled,
            'final_enabled' => $coinsEnabled,
            'server_id' => $serverId,
        ]);

        if (!$coinsEnabled) {
            return response()->json([
                'can_watch_ad' => false,
                'message' => $nodeEnabled ? 'Hệ thống xu đang bị tắt.' : 'Hệ thống xu không khả dụng cho máy chủ này.',
                'next_available' => null,
                'system_enabled' => false,
            ]);
        }

        $user = $this->request->user();
        $lastAdWatch = UserAdWatch::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->first();
        
        $canWatchAd = true;
        $message = null;
        
        // If the user has watched an ad before, check if enough time has passed
        if ($lastAdWatch) {
            $cooldownHours = config('pterodactyl.coins.ad_cooldown_hours', 6);
            $nextAvailableTime = $lastAdWatch->created_at->addHours($cooldownHours);
            
            if ($nextAvailableTime->isFuture()) {
                $canWatchAd = false;
                $timeLeft = now()->diffForHumans($nextAvailableTime, ['syntax' => \Carbon\CarbonInterface::DIFF_ABSOLUTE]);
                $message = "Bạn cần chờ thêm {$timeLeft} trước khi xem quảng cáo tiếp theo.";
            }
        }
        
        return response()->json([
            'can_watch_ad' => $canWatchAd,
            'message' => $message,
            'next_available' => $lastAdWatch && !$canWatchAd 
                ? $lastAdWatch->created_at->addHours($cooldownHours)->toIso8601String()
                : null,
        ]);
    }

    /**
     * Get the current balance and status of coins for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        /** @var \Pterodactyl\Models\User $user */
        $user = $this->request->user();
        
        // Debug: Hiển thị thông tin user
        \Log::debug('User coins debug:', [
            'user_id' => $user->id,
            'coins' => $user->coins,
            'has_coins_attribute' => isset($user->coins),
        ]);
        
        // IMPORTANT: Check if coin system is enabled by directly reading .env file
        $systemEnabled = false;
        $envPath = base_path('.env');
        if (file_exists($envPath)) {
            $envContent = file_get_contents($envPath);
            if (preg_match('/^PTERODACTYL_COINS_ENABLED=(.*)$/m', $envContent, $matches)) {
                $systemEnabled = trim($matches[1]) === 'true';
            }
        }
        
        // Get active server if available
        $serverId = request()->input('server_id');
        $nodeEnabled = true;
        
        if ($serverId) {
            $server = \Pterodactyl\Models\Server::where('id', $serverId)
                ->where('user_id', $user->id)
                ->first();
                
            if ($server) {
                $node = $server->node;
                if ($node) {
                    $nodeEnabled = $node->coins_enabled;
                }
            }
        }
        
        // Final check - both system and node must have coins enabled
        $coinsEnabled = $systemEnabled && $nodeEnabled;
        
        // Log all values for debugging
        \Log::debug('Index - Coin system enabled:', [
            'config_value' => config('pterodactyl.coins.enabled'),
            'env_value' => env('PTERODACTYL_COINS_ENABLED'),
            'direct_env_read' => $systemEnabled,
            'node_enabled' => $nodeEnabled,
            'final_enabled' => $coinsEnabled,
            'server_id' => $serverId,
        ]);
        
        // Lấy thông tin về lần xem quảng cáo cuối cùng
        $lastAdWatch = UserAdWatch::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->first();
            
        $canWatchAd = $coinsEnabled; // Mặc định có thể xem quảng cáo nếu hệ thống bật
        $nextAdAvailable = null;
        
        if ($lastAdWatch && $coinsEnabled) {
            $cooldownHours = config('pterodactyl.coins.ad_cooldown_hours', 6);
            $nextAvailableTime = $lastAdWatch->created_at->addHours($cooldownHours);
            
            $canWatchAd = !$nextAvailableTime->isFuture();
            
            if (!$canWatchAd) {
                $nextAdAvailable = now()->diffForHumans($nextAvailableTime, ['syntax' => \Carbon\CarbonInterface::DIFF_ABSOLUTE]);
            }
        }
        
        $responseData = [
            'data' => [
                'current_balance' => (int) ($user->coins ?? 0),
                'can_watch_ad' => $canWatchAd,
                'next_ad_available' => $nextAdAvailable,
                'last_watched' => $lastAdWatch ? $lastAdWatch->created_at->toIso8601String() : null,
                'system_enabled' => $coinsEnabled,
            ]
        ];
        
        \Log::debug('API response data:', $responseData);
        
        return response()->json($responseData);
    }
} 