<?php

namespace Pterodactyl\Http\Controllers\Admin\PocketAddons;

use Illuminate\Http\Request;
use Prologue\Alerts\AlertsMessageBag;
use Pterodactyl\Http\Controllers\Controller;
use Pterodactyl\Models\PocketAddonsConfig;

class PocketAddonsController extends Controller
{

    public function __construct(
        protected AlertsMessageBag $alert,
    )
    {
    }

    public function index(Request $request)
    {

        $config = PocketAddonsConfig::first();

        return view('admin.pocketaddons.index', [
            'config' => $config,
        ]);
    }

    public function update(Request $request)
    {
        $config = PocketAddonsConfig::first();

        $config->pe_eggs = $request->input('pe_eggs') ?? 0;
        $config->vabe_eggs = $request->input('vabe_eggs') ?? 0;

        $config->save();

        $this->alert->success('Successfully updated PocketAddons config.')->flash();

        return redirect()->route('admin.pocketaddons');
    }
}