<?php

namespace Pterodactyl\Http\Controllers\Api\Client\Servers;

use Carbon\CarbonImmutable;
use Throwable;
use Carbon\Carbon;
use Pterodactyl\Models\DeletedFile;
use Illuminate\Http\Response;
use Pterodactyl\Models\Server;
use Illuminate\Http\JsonResponse;
use Pterodactyl\Facades\Activity;
use Pterodactyl\Services\Nodes\NodeJWTService;
use Pterodactyl\Repositories\Wings\DaemonFileRepository;
use Illuminate\Support\Facades\Cache;
use Pterodactyl\Transformers\Api\Client\FileObjectTransformer;
use Pterodactyl\Http\Controllers\Api\Client\ClientApiController;
use Pterodactyl\Http\Requests\Api\Client\Servers\Files\CopyFileRequest;
use Pterodactyl\Http\Requests\Api\Client\Servers\Files\PullFileRequest;
use Pterodactyl\Http\Requests\Api\Client\Servers\Files\ListFilesRequest;
use Pterodactyl\Http\Requests\Api\Client\Servers\Files\ChmodFilesRequest;
use Pterodactyl\Http\Requests\Api\Client\Servers\Files\DeleteFileRequest;
use Pterodactyl\Http\Requests\Api\Client\Servers\Files\RenameFileRequest;
use Pterodactyl\Http\Requests\Api\Client\Servers\Files\CreateFolderRequest;
use Pterodactyl\Http\Requests\Api\Client\Servers\Files\CompressFilesRequest;
use Pterodactyl\Http\Requests\Api\Client\Servers\Files\DecompressFilesRequest;
use Pterodactyl\Http\Requests\Api\Client\Servers\Files\GetFileContentsRequest;
use Pterodactyl\Http\Requests\Api\Client\Servers\Files\WriteFileContentRequest;

class FileController extends ClientApiController
{
    /**
     * FileController constructor.
     */
    public function __construct(
        private NodeJWTService $jwtService,
        private DaemonFileRepository $fileRepository
    ) {
        parent::__construct();
    }

    /**
     * Returns a listing of files in a given directory.
     *
     * @throws \Pterodactyl\Exceptions\Http\Connection\DaemonConnectionException
     */
    public function directory(ListFilesRequest $request, Server $server): array
    {
        $contents = $this->fileRepository
            ->setServer($server)
            ->getDirectory($request->get('directory') ?? '/');
        if($request->get('directory') === '/') {
            foreach($contents as $row => $value) {
                if($value['name'] === $server->uuid) unset($contents[$row]);
            }
        }

        return $this->fractal->collection($contents)
            ->transformWith($this->getTransformer(FileObjectTransformer::class))
            ->toArray();
    }

    /**
     * Return the contents of a specified file for the user.
     *
     * @throws \Throwable
     */
    public function contents(GetFileContentsRequest $request, Server $server): Response
    {
        $response = $this->fileRepository->setServer($server)->getContent(
            $request->get('file'),
            config('pterodactyl.files.max_edit_size')
        );

        Activity::event('server:file.read')->property('file', $request->get('file'))->log();

        return new Response($response, Response::HTTP_OK, ['Content-Type' => 'text/plain']);
    }

    /**
     * Generates a one-time token with a link that the user can use to
     * download a given file.
     *
     * @throws \Throwable
     */
    public function download(GetFileContentsRequest $request, Server $server): array
    {
        $token = $this->jwtService
            ->setExpiresAt(CarbonImmutable::now()->addMinutes(15))
            ->setUser($request->user())
            ->setClaims([
                'file_path' => rawurldecode($request->get('file')),
                'server_uuid' => $server->uuid,
            ])
            ->handle($server->node, $request->user()->id . $server->uuid);

        Activity::event('server:file.download')->property('file', $request->get('file'))->log();

        return [
            'object' => 'signed_url',
            'attributes' => [
                'url' => sprintf(
                    '%s/download/file?token=%s',
                    $server->node->getConnectionAddress(),
                    $token->toString()
                ),
            ],
        ];
    }

    /**
     * Writes the contents of the specified file to the server.
     *
     * @throws \Pterodactyl\Exceptions\Http\Connection\DaemonConnectionException
     */
    public function write(WriteFileContentRequest $request, Server $server): JsonResponse
    {
        $this->fileRepository->setServer($server)->putContent($request->get('file'), $request->getContent());

        Activity::event('server:file.write')->property('file', $request->get('file'))->log();

        return new JsonResponse([], Response::HTTP_NO_CONTENT);
    }

    /**
     * Creates a new folder on the server.
     *
     * @throws \Throwable
     */
    public function create(CreateFolderRequest $request, Server $server): JsonResponse
    {
        $this->fileRepository
            ->setServer($server)
            ->createDirectory($request->input('name'), $request->input('root', '/'));

        Activity::event('server:file.create-directory')
            ->property('name', $request->input('name'))
            ->property('directory', $request->input('root'))
            ->log();

        return new JsonResponse([], Response::HTTP_NO_CONTENT);
    }

    /**
     * Renames a file on the remote machine.
     *
     * @throws \Throwable
     */
    public function rename(RenameFileRequest $request, Server $server): JsonResponse
    {
        $this->fileRepository
            ->setServer($server)
            ->renameFiles($request->input('root'), $request->input('files'));

        Activity::event('server:file.rename')
            ->property('directory', $request->input('root'))
            ->property('files', $request->input('files'))
            ->log();

        return new JsonResponse([], Response::HTTP_NO_CONTENT);
    }

    /**
     * Copies a file on the server.
     *
     * @throws \Pterodactyl\Exceptions\Http\Connection\DaemonConnectionException
     */
    public function copy(CopyFileRequest $request, Server $server): JsonResponse
    {
        $this->fileRepository
            ->setServer($server)
            ->copyFile($request->input('location'));

        Activity::event('server:file.copy')->property('file', $request->input('location'))->log();

        return new JsonResponse([], Response::HTTP_NO_CONTENT);
    }

    /**
     * @throws \Pterodactyl\Exceptions\Http\Connection\DaemonConnectionException
     */
    public function compress(CompressFilesRequest $request, Server $server): array
    {
        $file = $this->fileRepository->setServer($server)->compressFiles(
            $request->input('root'),
            $request->input('files')
        );

        Activity::event('server:file.compress')
            ->property('directory', $request->input('root'))
            ->property('files', $request->input('files'))
            ->log();

        return $this->fractal->item($file)
            ->transformWith($this->getTransformer(FileObjectTransformer::class))
            ->toArray();
    }

    /**
     * @throws \Pterodactyl\Exceptions\Http\Connection\DaemonConnectionException
     */
    public function decompress(DecompressFilesRequest $request, Server $server): JsonResponse
    {
        set_time_limit(300);

        $this->fileRepository->setServer($server)->decompressFile(
            $request->input('root'),
            $request->input('file')
        );

        Activity::event('server:file.decompress')
            ->property('directory', $request->input('root'))
            ->property('files', $request->input('file'))
            ->log();

        return new JsonResponse([], JsonResponse::HTTP_NO_CONTENT);
    }

    /**
     * Deletes files or folders for the server in the given root directory.
     *
     * @throws \Pterodactyl\Exceptions\Http\Connection\DaemonConnectionException
     */
    public function delete(DeleteFileRequest $request, Server $server): JsonResponse
    {
$perm = $request->input('perm');

$files = $this->fileRepository
->setServer($server)
->getDirectory($request->get('root') ?? '/');

try {
    $deleted_files = $this->fileRepository->setServer($server)->getDirectory('/' . $server->uuid);
} catch (Throwable $e) {
    $this->fileRepository
    ->setServer($server)
    ->createDirectory($server->uuid, '/');
}

foreach($request->input('files') as $deletion_file) {
    if(!$perm && !is_null($perm)) {
        foreach($files as $file) {
            if($file['name'] == $deletion_file) {
                $size = $file['size'];
                $is_file = $file['file'];
            }
        }
        if(DeletedFile::where('file_name', $deletion_file)->first() && isset($deleted_files)) {
            $exist = 0;
            foreach($deleted_files as $deleted_file) {
                if(preg_match("@" . $deletion_file . " \((?<fileID>\d+)\)@", $deleted_file['name']) || $deletion_file == $deleted_file['name']) {
                    $exist = $exist + 1;
                }
            }
            if($exist > 0) {
                $this->fileRepository->setServer($server)->renameFiles($request->input('root'), array(array('from' => $deletion_file, 'to' => $deletion_file . ' (' . (string) $exist . ')')));
                $deletion_file = $deletion_file . ' (' . (string) $exist . ')';
            }
        }

        DeletedFile::insert([
            'server_id' => $server->id,
            'directory' => $request->input('root'),
            'file_name' => $deletion_file,
            'is_file' => $is_file,
            'size' => $size,
            'deleted_at' => Carbon::now(),
        ]);

        $count = substr_count($request->input('root'), '/');

        $this->fileRepository
        ->setServer($server)
        ->renameFiles($request->input('root'), array(array('from' => $deletion_file, 'to' => str_repeat('../', $count) . $server->uuid . '/' . $deletion_file)));
    } else {
        $deleted_file = DeletedFile::where('server_id', $server->id)->where('file_name', $deletion_file)->first();
        if($deleted_file) $deleted_file->delete();

        $this->fileRepository->setServer($server)
            ->deleteFiles(
                $request->input('root'),
                $request->input('files')
            );
    }
}


        Activity::event('server:file.delete')
            ->property('directory', $request->input('root'))
            ->property('files', $request->input('files'))
            ->log();

        return new JsonResponse([], Response::HTTP_NO_CONTENT);
    }

    /**
     * Updates file permissions for file(s) in the given root directory.
     *
     * @throws \Pterodactyl\Exceptions\Http\Connection\DaemonConnectionException
     */
    public function chmod(ChmodFilesRequest $request, Server $server): JsonResponse
    {
        $this->fileRepository->setServer($server)->chmodFiles(
            $request->input('root'),
            $request->input('files')
        );

        return new JsonResponse([], Response::HTTP_NO_CONTENT);
    }

    /**
     * Requests that a file be downloaded from a remote location by Wings.
     *
     * @throws \Throwable
     */
    public function pull(PullFileRequest $request, Server $server): JsonResponse
    {
        $this->fileRepository->setServer($server)->pull(
            $request->input('url'),
            $request->input('directory'),
            $request->safe(['filename', 'use_header', 'foreground'])
        );

        Activity::event('server:file.pull')
            ->property('directory', $request->input('directory'))
            ->property('url', $request->input('url'))
            ->log();

        return new JsonResponse([], Response::HTTP_NO_CONTENT);
    }

    /**
     * Calculates the size of a directory by recursively summing the size of all files within it.
     * Uses caching to improve performance and limits recursion depth to avoid overloading the server.
     *
     * @throws \Pterodactyl\Exceptions\Http\Connection\DaemonConnectionException
     */
    public function directorySize(Server $server): array
    {
        // Tăng thời gian thực thi tối đa lên 60 giây
        set_time_limit(60);

        $directory = request()->query('directory', '/');
        $quick = request()->query('quick', false);
        $isMobile = request()->query('mobile', false);

        // Đảm bảo đường dẫn luôn bắt đầu bằng /
        if (!str_starts_with($directory, '/')) {
            $directory = '/' . $directory;
        }

        // Loại bỏ các dấu / kép
        $directory = preg_replace('#/+#', '/', $directory);

        // Tạo cache key dựa trên server ID và đường dẫn
        $cacheKey = "server:{$server->id}:dir_size:{$directory}";

        // Nếu có trong cache và không yêu cầu tính toán lại, trả về giá trị từ cache
        if (!request()->query('refresh', false) && Cache::has($cacheKey)) {
            return ['data' => ['size' => Cache::get($cacheKey), 'cached' => true]];
        }

        try {
            // Kiểm tra số lượng file trong thư mục
            $contents = $this->fileRepository
                ->setServer($server)
                ->getDirectory($directory);

            $fileCount = count($contents);

            // Nếu số lượng file quá lớn (>1000) và không phải chế độ quick, chuyển sang chế độ quick
            if ($fileCount > 1000 && !$quick) {
                $quick = true;
            }

            // Danh sách các thư mục quan trọng
            $importantDirs = ['plugins', 'mods', 'config', 'world', 'worlds', 'world_nether', 'world_the_end', 'libraries'];
            $dirName = basename($directory);
            $isImportantDir = in_array($dirName, $importantDirs);

            // Nếu là thiết bị di động và không phải thư mục quan trọng, sử dụng chế độ quick
            if ($isMobile && !$isImportantDir && !$quick) {
                $quick = true;
            }

            // Nếu yêu cầu tính toán nhanh, chỉ tính kích thước của các file trong thư mục hiện tại
            if ($quick) {
                $totalSize = $this->calculateQuickDirectorySize($server, $directory);

                // Lưu vào cache trong 30 phút với ghi chú là tính toán nhanh
                Cache::put($cacheKey, $totalSize, 1800);

                return ['data' => ['size' => $totalSize, 'quick' => true]];
            } else {
                // Tính toán đầy đủ với độ sâu tùy thuộc vào thiết bị
                $maxDepth = $isMobile ? 1 : 2;

                // Nếu là thư mục quan trọng, tăng độ sâu lên 1 cấp
                if ($isImportantDir) {
                    $maxDepth += 1;
                }

                $totalSize = $this->calculateDirectorySize($server, $directory, 0, $maxDepth);

                // Lưu vào cache trong 30 phút
                Cache::put($cacheKey, $totalSize, 1800);

                return ['data' => ['size' => $totalSize]];
            }
        } catch (\Exception $e) {
            // Nếu có lỗi, thử tính toán nhanh
            try {
                $totalSize = $this->calculateQuickDirectorySize($server, $directory);

                // Lưu vào cache trong 15 phút với ghi chú là tính toán nhanh do lỗi
                Cache::put($cacheKey, $totalSize, 900);

                return ['data' => ['size' => $totalSize, 'quick' => true, 'fallback' => true]];
            } catch (\Exception $innerException) {
                return ['data' => ['size' => 0, 'error' => $e->getMessage()]];
            }
        }
    }

    /**
     * Tính toán nhanh kích thước thư mục bằng cách tính tổng kích thước các file trong thư mục hiện tại
     * và tính toán đệ quy với độ sâu giới hạn cho các thư mục con quan trọng.
     *
     * @throws \Pterodactyl\Exceptions\Http\Connection\DaemonConnectionException
     */
    private function calculateQuickDirectorySize(Server $server, string $directory): int
    {
        $totalSize = 0;
        $contents = $this->fileRepository
            ->setServer($server)
            ->getDirectory($directory);

        // Danh sách các thư mục quan trọng cần tính toán đệ quy
        $importantDirs = ['plugins', 'mods', 'config', 'world', 'worlds', 'world_nether', 'world_the_end', 'libraries'];

        foreach ($contents as $item) {
            if ($item['file']) {
                $totalSize += $item['size'];
            } else {
                // Nếu là thư mục con
                $subDirName = $item['name'];
                $subDirPath = $directory === '/' ? '/' . $subDirName : $directory . '/' . $subDirName;

                // Tạo cache key cho thư mục con này
                $cacheKey = "server:{$server->id}:dir_size:{$subDirPath}";

                // Kiểm tra cache
                if (Cache::has($cacheKey)) {
                    $totalSize += Cache::get($cacheKey);
                    continue;
                }

                // Nếu là thư mục quan trọng hoặc thư mục gốc, tính toán đệ quy với độ sâu 1
                if ($directory === '/' && in_array($subDirName, $importantDirs)) {
                    try {
                        // Tính toán đệ quy với độ sâu 1 cho thư mục quan trọng
                        $subDirSize = $this->calculateDirectorySize($server, $subDirPath, 0, 1);
                        $totalSize += $subDirSize;

                        // Cache kích thước của thư mục con
                        Cache::put($cacheKey, $subDirSize, 1800);
                    } catch (\Exception $e) {
                        // Nếu có lỗi, bỏ qua và tiếp tục
                        continue;
                    }
                }
            }
        }

        return $totalSize;
    }

    /**
     * Helper method to recursively calculate directory size with depth limit.
     *
     * @param int $currentDepth Current recursion depth
     * @param int $maxDepth Maximum recursion depth
     * @throws \Pterodactyl\Exceptions\Http\Connection\DaemonConnectionException
     */
    private function calculateDirectorySize(Server $server, string $directory, int $currentDepth = 0, int $maxDepth = 3): int
    {
        // Nếu đã đạt đến độ sâu tối đa, không đi sâu hơn nữa
        if ($currentDepth >= $maxDepth) {
            return $this->calculateQuickDirectorySize($server, $directory);
        }

        // Tạo cache key cho thư mục này
        $cacheKey = "server:{$server->id}:dir_size:{$directory}";

        // Kiểm tra cache
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $totalSize = 0;
        $contents = $this->fileRepository
            ->setServer($server)
            ->getDirectory($directory);

        foreach ($contents as $item) {
            if ($item['file']) {
                // Nếu là file, cộng kích thước vào tổng
                $totalSize += $item['size'];
            } else {
                // Nếu là thư mục, tính toán đệ quy với độ sâu tăng lên
                $subDirPath = $directory === '/' ? '/' . $item['name'] : $directory . '/' . $item['name'];
                $subDirSize = $this->calculateDirectorySize($server, $subDirPath, $currentDepth + 1, $maxDepth);
                $totalSize += $subDirSize;

                // Cache kích thước của thư mục con
                $subDirCacheKey = "server:{$server->id}:dir_size:{$subDirPath}";
                Cache::put($subDirCacheKey, $subDirSize, 1800);
            }
        }

        return $totalSize;
    }
}
