<?php

namespace <PERSON>terodactyl\BlueprintFramework\Extensions\pullfiles;

use Illuminate\Support\Facades\Cache;
use Pterodactyl\Models\Server;
use Pterodactyl\Facades\Activity;
use Pterodactyl\Http\Controllers\Api\Client\ClientApiController;
use Pterodactyl\Repositories\Wings\DaemonCommandRepository;
use Pterodactyl\Repositories\Wings\DaemonFileRepository;
use Illuminate\Http\JsonResponse;
use Pterodactyl\Exceptions\DisplayException;
use Pterodactyl\Repositories\Wings\DaemonServerRepository;

class PullFilesController extends ClientApiController
{
    public function __construct(
        private DaemonFileRepository $fileRepository,
        private DaemonCommandRepository $commandRepository,
        private DaemonServerRepository $serverRepository,
    ) {
        parent::__construct();
    }

    private function returnFinalRedirect(string $url, int $max = 5, int $used = 0, string|null $prev = null): string
    {
        if ($used >= $max) {
            return $url;
        }

        if (str_starts_with($url, '/')) {
            $host = parse_url($prev, PHP_URL_HOST);

            if (!$host) {
                throw new DisplayException('Failed to determine host.');
            }

            $url = sprintf('%s://%s%s', parse_url($prev, PHP_URL_SCHEME), $host, $url);
        }

        $response = get_headers($url, true);

        if (!$response) {
            throw new DisplayException('Failed to query URL.');
        }

        $response = array_change_key_case($response, CASE_LOWER);

        if (array_key_exists('location', $response)) {
            try {
                if (is_array($response['location'])) {
                    return $this->returnFinalRedirect($response['location'][count($response['location']) - 1], $max, $used + 1, $url);
                } else {
                    return $this->returnFinalRedirect($response['location'], $max, $used + 1, $url);
                }
            } catch (\Exception $e) {
                return $url;
            }
        }

        return $url;
    }

    private function queryUrl(string $url): array
    {
        $data = Cache::remember("pullfiles:queryUrl:$url", 60, function () use ($url) {
            $realUrl = $this->returnFinalRedirect($url);
            $response = get_headers($realUrl, true);

            if (!$response) {
                throw new DisplayException('Failed to query URL.');
            }

            $response = array_change_key_case($response, CASE_LOWER);

            $contentDisposition = array_key_exists('content-disposition', $response) ? $response['content-disposition'] : null;
            if ($contentDisposition) {
                $matches = [];
                preg_match('/filename(\*)?=(UTF-8\'\')?"?([^";]+)"?;?/', $contentDisposition, $matches);

                if (count($matches) > 2) {
                    return [
                        'filename' => iconv_mime_decode($matches[3]),
                        'size' => array_key_exists('content-length', $response) ? (int) $response['content-length'] : null,
                    ];
                }
            }

            return [
                'filename' => null,
                'size' => array_key_exists('content-length', $response) ? (int) $response['content-length'] : null,
            ];
        });

        if (!$data['filename']) {
            $data['filename'] = basename($url);
        }

        if ($data['size'] <= 1024) {
            throw new DisplayException('Failed to determine file size. Make sure the url is a direct link to the file.');
        }

        return $data;
    }

    private function details(Server $server): array
    {
        $details = Cache::remember("server:{$server->id}:details", 15, function () use ($server) {
            try {
                return $this->serverRepository->setServer($server)->getDetails();
            } catch (\Exception $exception) {
                return [
                    'utilization' => [
                        'disk_bytes' => 0,
                    ],
                ];
            }
        });

        return [
            'disk_used' => $details['utilization']['disk_bytes'],
            'disk_limit' => $server->disk * 1024 * 1024,
        ];
    }

    public function query(PullFilesQueryRequest $request): JsonResponse
    {
        $data = $this->queryUrl($request->input('url'));

        return new JsonResponse([
            'filename' => $data['filename'],
            'size' => $data['size'],
        ]);
    }

    public function pull(PullFilesPullRequest $request, Server $server): JsonResponse
    {
        $data = $this->queryUrl($request->input('url'));
        $details = $this->details($server);

        if ($details['disk_used'] + $data['size'] > $details['disk_limit']) {
            throw new DisplayException('Insufficient disk space.');
        }

        $identifier = $this->fileRepository->setServer($server)->pull(
            $this->returnFinalRedirect($request->input('url')),
            $request->input('directory'),
            [
                'filename' => $request->input('filename', $data['filename']),
                'use_header' => false,
                'foreground' => false,
            ],
        );

        Activity::event('server:file.pull')
            ->property('directory', $request->input('directory'))
            ->property('url', $request->input('url'))
            ->property('filename', $request->input('filename', $data['filename']))
            ->log();

        return new JsonResponse([
            'id' => json_decode($identifier->getBody(), true)['identifier'],
        ]);
    }

    public function cancel(PullFilesCancelRequest $request, Server $server, string $id): JsonResponse
    {
        $this->fileRepository->setServer($server)->getHttpClient()->delete(
            sprintf('/api/servers/%s/files/pull/%s', $server->uuid, $id),
        );

        return new JsonResponse([], JsonResponse::HTTP_NO_CONTENT);
    }
}
