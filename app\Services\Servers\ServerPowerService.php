<?php

namespace Pterodactyl\Services\Servers;

use Pterodactyl\Models\Server;
use Pterodactyl\Repositories\Wings\DaemonPowerRepository;
use Pterodactyl\Repositories\Wings\DaemonCommandRepository;
use Pterodactyl\Exceptions\Http\Connection\DaemonConnectionException;
use Illuminate\Support\Facades\Log;

class ServerPowerService
{
    /**
     * ServerPowerService constructor.
     */
    public function __construct(
        private DaemonPowerRepository $powerRepository,
        private DaemonCommandRepository $commandRepository
    ) {
    }

    /**
     * Send a power action to the server with save-all before stop.
     */
    public function sendPowerAction(Server $server, string $action): void
    {
        if ($action === 'stop') {
            $this->stopWithSaveAll($server);
        } else {
            $this->powerRepository->setServer($server)->send($action);
        }
    }

    /**
     * Stop server with save-all command first.
     */
    private function stopWithSaveAll(Server $server): void
    {
        Log::info("Stopping server {$server->name} (ID: {$server->id}) with save-all command first");

        try {
            // Send save-all command first to save all game data
            Log::info("Sending save-all command to server {$server->name}");
            $this->commandRepository->setServer($server)->send('save-all');

            // Wait a moment for the save to complete (increased to 3 seconds for better reliability)
            Log::info("Waiting 3 seconds for save-all to complete on server {$server->name}");
            sleep(3);

            // Then stop the server gracefully
            Log::info("Sending stop command to server {$server->name}");
            $this->powerRepository->setServer($server)->send('stop');

            Log::info("Successfully initiated stop sequence for server {$server->name}");
        } catch (DaemonConnectionException $exception) {
            // If save-all fails, still try to stop the server to prevent hanging
            Log::warning("Save-all command failed for server {$server->name}, proceeding with stop: " . $exception->getMessage());
            $this->powerRepository->setServer($server)->send('stop');
        }
    }
}
