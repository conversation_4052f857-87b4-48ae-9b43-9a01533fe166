<?php

namespace Pterodactyl\Http\Controllers\Api\Client\Coins;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;
use Pterodactyl\Http\Controllers\Controller;
use Pterodactyl\Services\Coins\CoinsService;
use Pterodactyl\Transformers\Api\Client\BaseClientTransformer;
use Pterodactyl\Repositories\Wings\DaemonServerRepository;
use Pterodactyl\Repositories\Wings\DaemonPowerRepository;
use Pterodactyl\Models\AdHistory;
use Carbon\Carbon;

class CoinsController extends Controller
{
    /**
     * @var \Pterodactyl\Services\Coins\CoinsService
     */
    private $coinsService;

    /**
     * CoinsController constructor.
     *
     * @param \Pterodactyl\Services\Coins\CoinsService $coinsService
     */
    public function __construct(CoinsService $coinsService)
    {
        $this->coinsService = $coinsService;
    }

    /**
     * Check if the user can watch an ad to earn coins.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkAdAvailability(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Check when the user last watched an ad
        $lastWatch = AdHistory::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->first();
            
        // If user has never watched an ad or the last watch was more than 24 hours ago
        if (!$lastWatch || Carbon::parse($lastWatch->created_at)->addHours(24)->isPast()) {
            return new JsonResponse([
                'data' => [
                    'canWatchAd' => true,
                ],
            ]);
        } else {
            // Calculate when they can watch next
            $nextAvailable = Carbon::parse($lastWatch->created_at)->addHours(24);
            
            return new JsonResponse([
                'data' => [
                    'canWatchAd' => false,
                    'message' => 'You can watch another ad in ' . $nextAvailable->diffForHumans(null, true) . '.',
                    'nextAvailableDate' => $nextAvailable->toIso8601String(),
                ],
            ]);
        }
    }
} 