<?php

namespace Pterodactyl\BlueprintFramework\Extensions\templateinstaller\Models;

use Pterodactyl\Models\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Template.
 *
 * @property int $id
 * @property int $order
 * @property array $eggs
 * @property string $name
 * @property string $category
 * @property string $description
 * @property string|null $full_description
 * @property string $author
 * @property string $version
 * @property string|null $icon
 * @property string|null $startup_command
 * @property bool $selectable
 * @property bool $force_reinstall
 * @property bool $force_wipe
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @method static \Database\Factories\Template factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Template newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Template newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Template query()
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereId($value)
 *
 * @mixin \Eloquent
 */
class Template extends Model
{
    /**
     * The resource name for this model when it is transformed into an
     * API representation using fractal.
     */
    public const RESOURCE_NAME = 'template';

    /**
     * The table associated with the model.
     */
    protected $table = 'templates';

    /**
     * Cast values to correct type.
     */
    protected $casts = [
        'eggs' => 'array',
        'selectable' => 'bool',
        'force_wipe' => 'bool',
        'force_reinstall' => 'bool',
        self::CREATED_AT => 'datetime',
        self::UPDATED_AT => 'datetime',
    ];

    /**
     * Fields that are mass assignable.
     */
    protected $fillable = [
        'order',
        'eggs',
        'name',
        'category',
        'description',
        'full_description',
        'author',
        'version',
        'icon',
        'startup_command',
        'selectable',
        'force_reinstall',
        'force_wipe',
    ];

    /**
     * Rules to protect against invalid data entry to DB.
     */
    public static array $validationRules = [
        'order' => 'required|integer',
        'eggs' => 'required|array',
        'name' => 'required|string',
        'category' => 'required|string',
        'description' => 'required|string',
        'full_description' => 'nullable|string',
        'author' => 'required|string',
        'version' => 'required|string',
        'icon' => 'nullable|string',
        'startup_command' => 'nullable|string',
        'selectable' => 'required|boolean',
        'force_reinstall' => 'required|boolean',
        'force_wipe' => 'required|boolean',
    ];

    /**
     * Returns the variables associated with this template.
     */
    public function variables(): HasMany
    {
        return $this->hasMany(TemplateVariable::class, 'template_id');
    }

    /**
     * Returns the steps associated with this template.
     */
    public function steps(): HasMany
    {
        return $this->hasMany(TemplateStep::class, 'template_id');
    }
}
