<?php

namespace Pterodactyl\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ArixLang extends Command
{
    protected $signature = 'language:compile';
    protected $description = 'Compile all translations under resources/lang/[lang]/arix into a single translation.php file';

    public function handle()
    { 
        $langPath = resource_path('lang');

        foreach (File::directories($langPath) as $langDir) {
            $lang = basename($langDir);
            $arixPath = "$langDir/arix";

            if (!File::exists($arixPath)) {
                $this->info("Skipping '$lang': no 'arix' directory.");
                continue;
            }

            $compiled = $this->loadTranslationFiles($arixPath);

            $outputPath = "$langDir/translation.php";
            File::put($outputPath, "<?php\n\nreturn " . var_export(['arix' => $compiled], true) . ";\n");

            $this->info("Compiled translations for '$lang' into translation.php");
        }

        return Command::SUCCESS;
    }

    protected function loadTranslationFiles(string $dir): array
    {
        $translations = [];

        foreach (File::allFiles($dir) as $file) {
            $relativePath = str_replace([$dir . DIRECTORY_SEPARATOR, '.php'], '', $file->getPathname());
            $keys = explode(DIRECTORY_SEPARATOR, $relativePath);

            $data = File::getRequire($file->getPathname());

            if (gettype($data) != "array"){
                continue;
            }

            $nested = $this->arrayFromPath($keys, $data);
            $translations = array_merge_recursive($translations, $nested);
        }

        return $translations;
    }

    protected function arrayFromPath(array $path, array $value): array
    {
        if (empty($path)) {
            return $value;
        }

        return [$path[0] => $this->arrayFromPath(array_slice($path, 1), $value)];
    }
}
